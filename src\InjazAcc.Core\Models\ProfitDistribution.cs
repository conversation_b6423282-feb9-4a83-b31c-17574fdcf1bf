using System;
using System.Collections.Generic;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج توزيع الأرباح على الشركاء
    /// </summary>
    public class ProfitDistribution
    {
        public int Id { get; set; }
        public string ReferenceNumber { get; set; }
        public DateTime DistributionDate { get; set; }
        public decimal TotalAmount { get; set; }
        public string Description { get; set; }
        public int FiscalYearId { get; set; }
        
        // العلاقة مع السنة المالية
        public virtual FiscalYear FiscalYear { get; set; }
        
        // العلاقة مع المستخدم الذي أنشأ التوزيع
        public int UserId { get; set; }
        public virtual User User { get; set; }
        
        // العلاقة مع تفاصيل التوزيع
        public virtual ICollection<ProfitDistributionItem> Items { get; set; }
    }
}
