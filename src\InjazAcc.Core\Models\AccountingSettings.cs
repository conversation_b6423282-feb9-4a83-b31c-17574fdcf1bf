namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج إعدادات الحسابات المحاسبية
    /// </summary>
    public class AccountingSettings
    {
        public int Id { get; set; }
        
        // الحسابات الرئيسية
        public int TreasuryAccountId { get; set; }
        public int InventoryAccountId { get; set; }
        public int SalesAccountId { get; set; }
        public int CostOfSalesAccountId { get; set; }
        public int CustomersAccountId { get; set; }
        public int SuppliersAccountId { get; set; }
        public int SalesReturnAccountId { get; set; }
        public int PurchaseReturnAccountId { get; set; }
        public int SalesTaxAccountId { get; set; }
        public int PurchaseTaxAccountId { get; set; }
        public int SalesDiscountAccountId { get; set; }
        public int PurchaseDiscountAccountId { get; set; }
        
        // العلاقات
        public virtual Account TreasuryAccount { get; set; }
        public virtual Account InventoryAccount { get; set; }
        public virtual Account SalesAccount { get; set; }
        public virtual Account CostOfSalesAccount { get; set; }
        public virtual Account CustomersAccount { get; set; }
        public virtual Account SuppliersAccount { get; set; }
        public virtual Account SalesReturnAccount { get; set; }
        public virtual Account PurchaseReturnAccount { get; set; }
        public virtual Account SalesTaxAccount { get; set; }
        public virtual Account PurchaseTaxAccount { get; set; }
        public virtual Account SalesDiscountAccount { get; set; }
        public virtual Account PurchaseDiscountAccount { get; set; }
    }
}
