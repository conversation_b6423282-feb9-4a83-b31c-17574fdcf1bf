﻿#pragma checksum "..\..\..\..\..\Views\Inventory\InventoryPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "192F956C6F8BF6863EDD237DDDF1135271648C93"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using InjazAcc.UI.Views.Inventory;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InjazAcc.UI.Views.Inventory {
    
    
    /// <summary>
    /// InventoryPage
    /// </summary>
    public partial class InventoryPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 48 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbWarehouses;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSearch;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgInventory;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbInventoryWarehouses;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpInventoryDate;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgInventoryCount;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbMovementWarehouses;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbItems;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpFromDate;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpToDate;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgInventoryMovement;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InjazAcc.UI;component/views/inventory/inventorypage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.cmbWarehouses = ((System.Windows.Controls.ComboBox)(target));
            
            #line 48 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
            this.cmbWarehouses.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbWarehouses_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 53 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnAddWarehouse_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.txtSearch = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            
            #line 61 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnSearch_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 66 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnAddItem_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 67 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnExportToExcel_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.dgInventory = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 11:
            this.cmbInventoryWarehouses = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.dpInventoryDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 13:
            
            #line 138 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnStartNewInventory_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 139 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnPrintInventoryReport_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.dgInventoryCount = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 16:
            
            #line 158 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnSaveInventory_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 159 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnAdjustDifferences_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.cmbMovementWarehouses = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 19:
            this.cmbItems = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 20:
            this.dpFromDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 21:
            this.dpToDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 22:
            
            #line 214 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnShowMovement_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.dgInventoryMovement = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 8:
            
            #line 85 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnEditItem_Click);
            
            #line default
            #line hidden
            break;
            case 9:
            
            #line 88 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnDeleteItem_Click);
            
            #line default
            #line hidden
            break;
            case 10:
            
            #line 91 "..\..\..\..\..\Views\Inventory\InventoryPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnItemDetails_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

