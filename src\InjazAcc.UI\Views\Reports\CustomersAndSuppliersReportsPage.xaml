<Page x:Class="InjazAcc.UI.Views.Reports.CustomersAndSuppliersReportsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Reports"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="900"
      Title="تقارير العملاء والموردين"
      FlowDirection="RightToLeft"
      FontFamily="Arial"
      TextElement.FontSize="14"
      TextElement.FontWeight="Regular"
      TextElement.Foreground="{DynamicResource MaterialDesignBody}"
      Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة وزر الرجوع -->
        <Grid Grid.Row="0" Margin="20,20,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0" Style="{StaticResource MaterialDesignFlatButton}"
                    ToolTip="الرجوع لصفحة التقارير" Click="btnBackToReports_Click"
                    HorizontalAlignment="Left" Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ArrowRight" Width="24" Height="24" VerticalAlignment="Center"/>
                    <TextBlock Text="الرجوع للتقارير" VerticalAlignment="Center" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>

            <TextBlock Grid.Column="1" Text="تقارير العملاء والموردين" Style="{StaticResource PageTitle}"/>
        </Grid>

        <!-- أدوات التحكم -->
        <Grid Grid.Row="1" Margin="20,0,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- نوع التقرير -->
            <TextBlock Grid.Column="0" Text="نوع التقرير:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <ComboBox Grid.Column="1" x:Name="cmbReportType" Width="200" SelectedIndex="0" Margin="0,0,20,0" SelectionChanged="cmbReportType_SelectionChanged">
                <ComboBoxItem Content="كشف حساب عميل"/>
                <ComboBoxItem Content="كشف حساب مورد"/>
                <ComboBoxItem Content="أعمار ديون العملاء"/>
                <ComboBoxItem Content="أعمار ديون الموردين"/>
                <ComboBoxItem Content="تقرير أرصدة العملاء"/>
                <ComboBoxItem Content="تقرير أرصدة الموردين"/>
            </ComboBox>

            <!-- العميل/المورد -->
            <TextBlock Grid.Column="2" x:Name="lblCustomerSupplier" Text="العميل:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <ComboBox Grid.Column="3" x:Name="cmbCustomerSupplier" Width="200" SelectedIndex="0" Margin="0,0,20,0">
                <ComboBoxItem Content="الكل"/>
                <ComboBoxItem Content="شركة الأمل للتجارة"/>
                <ComboBoxItem Content="مؤسسة النور"/>
                <ComboBoxItem Content="شركة الصفا للمقاولات"/>
                <ComboBoxItem Content="مؤسسة الإبداع"/>
                <ComboBoxItem Content="شركة الوفاء"/>
            </ComboBox>

            <!-- الفترة -->
            <TextBlock Grid.Column="4" Text="من:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <DatePicker Grid.Column="5" x:Name="dpFromDate" Width="120" Margin="0,0,20,0"/>

            <TextBlock Grid.Column="6" Text="إلى:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <DatePicker Grid.Column="7" x:Name="dpToDate" Width="120" Margin="0,0,20,0"/>
        </Grid>

        <Grid Grid.Row="1" Margin="20,40,20,10" VerticalAlignment="Bottom">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- زر عرض التقرير -->
            <Button Grid.Column="1" Style="{StaticResource MaterialDesignRaisedButton}" Content="عرض التقرير" Click="btnShowReport_Click"/>
        </Grid>

        <!-- محتوى التقرير -->
        <Grid Grid.Row="2" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- جدول التقرير -->
            <DataGrid Grid.Row="0" x:Name="dgReport" AutoGenerateColumns="False" CanUserAddRows="False"
                      Style="{StaticResource DataGridStyle}" Margin="0,0,0,10">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=dd/MM/yyyy}" Width="100"/>
                    <DataGridTextColumn Header="رقم المستند" Binding="{Binding DocumentNumber}" Width="100"/>
                    <DataGridTextColumn Header="نوع المستند" Binding="{Binding DocumentType}" Width="120"/>
                    <DataGridTextColumn Header="البيان" Binding="{Binding Description}" Width="*"/>
                    <DataGridTextColumn Header="مدين" Binding="{Binding Debit, StringFormat=N2}" Width="120"/>
                    <DataGridTextColumn Header="دائن" Binding="{Binding Credit, StringFormat=N2}" Width="120"/>
                    <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance, StringFormat=N2}" Width="120"/>
                </DataGrid.Columns>
            </DataGrid>

            <!-- أزرار الإجراءات -->
            <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Left">
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="طباعة التقرير" Margin="0,0,10,0" Click="btnPrintReport_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="تصدير Excel" Margin="0,0,10,0" Click="btnExportExcel_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="تصدير PDF" Click="btnExportPDF_Click"/>
            </StackPanel>

            <!-- الإجماليات -->
            <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right">
                <TextBlock Text="إجمالي المدين:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="txtTotalDebit" Text="0.00" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>

                <TextBlock Text="إجمالي الدائن:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="txtTotalCredit" Text="0.00" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>

                <TextBlock Text="الرصيد:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="txtBalance" Text="0.00" FontWeight="Bold" VerticalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Page>
