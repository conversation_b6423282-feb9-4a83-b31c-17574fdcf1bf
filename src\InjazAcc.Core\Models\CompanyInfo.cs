using System;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج معلومات الشركة في النظام
    /// </summary>
    public class CompanyInfo
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string LegalName { get; set; }
        public string TaxNumber { get; set; }
        public string CommercialRegister { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Website { get; set; }
        public string LogoPath { get; set; }
        public string Currency { get; set; }
        public string CurrencySymbol { get; set; }
        public DateTime EstablishmentDate { get; set; }
        public int CurrentFiscalYearId { get; set; }
        
        // العلاقة مع السنة المالية الحالية
        public virtual FiscalYear CurrentFiscalYear { get; set; }
    }
}
