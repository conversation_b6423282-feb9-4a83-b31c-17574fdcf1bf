using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace InjazAcc.UI.Views.Sales
{
    /// <summary>
    /// Interaction logic for SimpleSalesPage.xaml
    /// </summary>
    public partial class SimpleSalesPage : Page
    {
        public SimpleSalesPage()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية لفواتير المبيعات
                var salesList = new List<SimpleSaleInvoice>
                {
                    new SimpleSaleInvoice { InvoiceNumber = "INV-001", InvoiceDate = DateTime.Now.AddDays(-1), CustomerName = "شركة الأمل التجارية", TotalAmount = 5250.50m, PaidAmount = 5250.50m, RemainingAmount = 0, Status = "مدفوعة" },
                    new SimpleSaleInvoice { InvoiceNumber = "INV-002", InvoiceDate = DateTime.Now.AddDays(-3), CustomerName = "مؤسسة النور", TotalAmount = 3750.75m, PaidAmount = 2000.00m, RemainingAmount = 1750.75m, Status = "مدفوعة جزئياً" },
                    new SimpleSaleInvoice { InvoiceNumber = "INV-003", InvoiceDate = DateTime.Now.AddDays(-5), CustomerName = "شركة الإعمار", TotalAmount = 8500.00m, PaidAmount = 0, RemainingAmount = 8500.00m, Status = "مؤكدة" },
                    new SimpleSaleInvoice { InvoiceNumber = "INV-004", InvoiceDate = DateTime.Now.AddDays(-7), CustomerName = "مؤسسة الفجر", TotalAmount = 4200.25m, PaidAmount = 4200.25m, RemainingAmount = 0, Status = "مدفوعة" },
                    new SimpleSaleInvoice { InvoiceNumber = "INV-005", InvoiceDate = DateTime.Now.AddDays(-10), CustomerName = "شركة البناء الحديث", TotalAmount = 3300.00m, PaidAmount = 1500.00m, RemainingAmount = 1800.00m, Status = "مدفوعة جزئياً" }
                };
                dgSales.ItemsSource = salesList;

                // بيانات تجريبية لمردودات المبيعات
                var returnsList = new List<SimpleSaleReturnInvoice>
                {
                    new SimpleSaleReturnInvoice { InvoiceNumber = "RET-001", InvoiceDate = DateTime.Now.AddDays(-2), CustomerName = "شركة الأمل التجارية", OriginalInvoiceNumber = "INV-001", TotalAmount = 750.50m, Status = "مؤكدة" },
                    new SimpleSaleReturnInvoice { InvoiceNumber = "RET-002", InvoiceDate = DateTime.Now.AddDays(-8), CustomerName = "مؤسسة الفجر", OriginalInvoiceNumber = "INV-004", TotalAmount = 800.25m, Status = "مؤكدة" }
                };
                dgReturns.ItemsSource = returnsList;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnNewSale_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة فاتورة مبيعات جديدة
                var saleInvoiceWindow = new NewSaleInvoiceWindow();
                saleInvoiceWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnNewReturn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة مردودات مبيعات جديدة
                var saleReturnWindow = new SaleReturnWindow();
                saleReturnWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSalesReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة تقرير المبيعات
                var salesReportWindow = new SalesReportWindow();
                salesReportWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void dgSales_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (dgSales.SelectedItem != null)
                {
                    var invoice = dgSales.SelectedItem as SimpleSaleInvoice;
                    if (invoice != null)
                    {
                        MessageBox.Show($"تم اختيار الفاتورة رقم {invoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void dgReturns_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (dgReturns.SelectedItem != null)
                {
                    var returnInvoice = dgReturns.SelectedItem as SimpleSaleReturnInvoice;
                    if (returnInvoice != null)
                    {
                        MessageBox.Show($"تم اختيار المردود رقم {returnInvoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnViewInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var invoice = button.DataContext as SimpleSaleInvoice;
                if (invoice != null)
                {
                    MessageBox.Show($"عرض الفاتورة رقم {invoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEditInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var invoice = button.DataContext as SimpleSaleInvoice;
                if (invoice != null)
                {
                    MessageBox.Show($"تعديل الفاتورة رقم {invoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var invoice = button.DataContext as SimpleSaleInvoice;
                if (invoice != null)
                {
                    MessageBox.Show($"طباعة الفاتورة رقم {invoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnDeleteInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var invoice = button.DataContext as SimpleSaleInvoice;
                if (invoice != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف الفاتورة رقم {invoice.InvoiceNumber}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (result == MessageBoxResult.Yes)
                    {
                        MessageBox.Show("تم حذف الفاتورة بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnViewReturn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var returnInvoice = button.DataContext as SimpleSaleReturnInvoice;
                if (returnInvoice != null)
                {
                    MessageBox.Show($"عرض المردود رقم {returnInvoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintReturn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var returnInvoice = button.DataContext as SimpleSaleReturnInvoice;
                if (returnInvoice != null)
                {
                    MessageBox.Show($"طباعة المردود رقم {returnInvoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnShowReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري عرض التقرير...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    // فئات البيانات
    public class SimpleSaleInvoice
    {
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public string CustomerName { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public string Status { get; set; }
    }

    public class SimpleSaleReturnInvoice
    {
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public string CustomerName { get; set; }
        public string OriginalInvoiceNumber { get; set; }
        public decimal TotalAmount { get; set; }
        public string Status { get; set; }
    }
}
