using System;
using System.Threading.Tasks;

namespace InjazAcc.Core.Interfaces
{
    /// <summary>
    /// واجهة خدمة الحفظ التلقائي
    /// </summary>
    public interface IAutoSaveService : IDisposable
    {
        /// <summary>
        /// تفعيل أو إلغاء تفعيل الحفظ التلقائي
        /// </summary>
        bool IsEnabled { get; set; }

        /// <summary>
        /// حفظ البيانات يدوياً
        /// </summary>
        /// <returns>true إذا تم الحفظ بنجاح، false في حالة الخطأ</returns>
        Task<bool> SaveNowAsync();
    }
}
