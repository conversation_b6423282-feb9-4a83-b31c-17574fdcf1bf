namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج إعدادات النظام
    /// </summary>
    public class SystemSettings
    {
        public int Id { get; set; }
        public string SettingKey { get; set; }
        public string SettingValue { get; set; }
        public string Description { get; set; }
        public SettingGroup Group { get; set; }
        public bool IsReadOnly { get; set; }
    }
    
    /// <summary>
    /// مجموعات الإعدادات
    /// </summary>
    public enum SettingGroup
    {
        General = 1,         // عام
        Invoice = 2,         // فواتير
        Inventory = 3,       // مخزون
        Accounting = 4,      // محاسبة
        Security = 5,        // أمان
        Appearance = 6,      // مظهر
        Notification = 7,    // إشعارات
        Backup = 8           // نسخ احتياطي
    }
}
