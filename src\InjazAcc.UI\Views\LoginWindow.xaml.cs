using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media.Animation;
using InjazAcc.Core.Exceptions;
using InjazAcc.Services.Helpers;

namespace InjazAcc.UI.Views
{
    /// <summary>
    /// Interaction logic for LoginWindow.xaml
    /// </summary>
    public partial class LoginWindow : Window
    {
        public LoginWindow()
        {
            InitializeComponent();
            txtUsername.Focus();

            // تعيين قيم افتراضية للتطوير
            #if DEBUG
            txtUsername.Text = "admin";
            txtPassword.Password = "admin";
            #endif
        }

        private void btnLogin_Click(object sender, RoutedEventArgs e)
        {
            PerformLogin();
        }

        private void txtUsername_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Return)
            {
                txtPassword.Focus();
            }
        }

        private void txtPassword_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Return)
            {
                PerformLogin();
            }
        }

        private void btnClose_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void PerformLogin()
        {
            try
            {
                // التحقق من إدخال اسم المستخدم وكلمة المرور باستخدام ملحقات التحقق
                txtUsername.Text.ThrowIfNullOrWhiteSpace("اسم المستخدم", "الرجاء إدخال اسم المستخدم");

                if (string.IsNullOrWhiteSpace(txtPassword.Password))
                {
                    throw new ValidationException("الرجاء إدخال كلمة المرور", ErrorCodes.RequiredValueMissing);
                }

                // إظهار مؤشر التحميل
                ShowLoading();

                // محاكاة عملية تسجيل الدخول (في الإصدار النهائي، يجب استخدام خدمة المصادقة)
                // في الإصدار التجريبي، نقبل أي اسم مستخدم وكلمة مرور
                System.Threading.Tasks.Task.Delay(1000).ContinueWith(_ =>
                {
                    Dispatcher.Invoke(() =>
                    {
                        try
                        {
                            // حفظ تفضيلات المستخدم إذا تم اختيار "تذكرني"
                            if (chkRememberMe.IsChecked == true)
                            {
                                // هنا يمكن حفظ بيانات المستخدم بطريقة آمنة
                                // مثال: Properties.Settings.Default.RememberUsername = true;
                                // Properties.Settings.Default.Username = txtUsername.Text;
                                // Properties.Settings.Default.Save();
                            }

                            // إخفاء رسالة الخطأ
                            HideError();

                            // فتح النافذة الرئيسية
                            var mainWindow = new MainWindow();
                            mainWindow.Show();

                            // إغلاق نافذة تسجيل الدخول
                            this.Close();
                        }
                        catch (Exception ex)
                        {
                            // إخفاء مؤشر التحميل
                            HideLoading();

                            // عرض رسالة الخطأ باستخدام نظام معالجة الاستثناءات الجديد
                            ex.ToInjazException("حدث خطأ أثناء تسجيل الدخول").Handle(true);
                        }
                    });
                });
            }
            catch (ValidationException ex)
            {
                // عرض رسالة خطأ التحقق
                ShowError(ex.Message);

                // تركيز على الحقل المناسب
                if (ex.Message.Contains("اسم المستخدم"))
                {
                    txtUsername.Focus();
                }
                else if (ex.Message.Contains("كلمة المرور"))
                {
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                // عرض رسالة الخطأ باستخدام نظام معالجة الاستثناءات الجديد
                ex.ToInjazException("حدث خطأ أثناء تسجيل الدخول").Handle(true);
            }
        }

        private void ShowLoading()
        {
            // هنا يمكن إظهار مؤشر التحميل
            btnLogin.IsEnabled = false;
            btnLogin.Content = "جاري تسجيل الدخول...";
        }

        private void HideLoading()
        {
            // هنا يمكن إخفاء مؤشر التحميل
            btnLogin.IsEnabled = true;
            btnLogin.Content = "تسجيل الدخول";
        }

        private void ShowError(string message)
        {
            txtError.Text = message;
            txtError.Visibility = Visibility.Visible;

            // إضافة تأثير حركي لجذب انتباه المستخدم
            var animation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromSeconds(0.3)
            };
            txtError.BeginAnimation(UIElement.OpacityProperty, animation);
        }

        private void HideError()
        {
            txtError.Text = string.Empty;
            txtError.Visibility = Visibility.Collapsed;
        }

        // إضافة إمكانية تحريك النافذة بالماوس
        protected override void OnMouseLeftButtonDown(MouseButtonEventArgs e)
        {
            try
            {
                base.OnMouseLeftButtonDown(e);
                if (e.ButtonState == MouseButtonState.Pressed)
                {
                    this.DragMove();
                }
            }
            catch (Exception ex)
            {
                // تجاهل الخطأ لتجنب توقف التطبيق
                Console.WriteLine($"خطأ في تحريك النافذة: {ex.Message}");
            }
        }
    }
}
