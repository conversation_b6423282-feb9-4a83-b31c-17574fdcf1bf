using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace InjazAcc.Core.Interfaces
{
    /// <summary>
    /// واجهة المستودع العامة للتعامل مع الكيانات
    /// </summary>
    public interface IRepository<T> where T : class
    {
        // عمليات القراءة
        Task<T> GetByIdAsync(int id);
        Task<IEnumerable<T>> GetAllAsync();
        Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);
        Task<T> SingleOrDefaultAsync(Expression<Func<T, bool>> predicate);
        Task<T> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate);
        Task<bool> AnyAsync(Expression<Func<T, bool>> predicate);
        Task<int> CountAsync(Expression<Func<T, bool>> predicate = null);

        // عمليات الكتابة
        Task AddAsync(T entity);
        Task AddRangeAsync(IEnumerable<T> entities);
        void Update(T entity);
        void UpdateRange(IEnumerable<T> entities);
        void Remove(T entity);
        void RemoveRange(IEnumerable<T> entities);

        // عمليات الاستعلام المتقدمة
        IQueryable<T> Query();
        Task<IEnumerable<T>> GetPagedAsync(int page, int pageSize);
    }
}
