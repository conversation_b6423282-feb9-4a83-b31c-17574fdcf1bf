using System;
using System.Collections.Generic;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج المنتج (الصنف) في النظام
    /// </summary>
    public class Product
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public decimal PurchasePrice { get; set; }
        public decimal SalePrice { get; set; }
        public decimal MinimumStock { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastUpdated { get; set; }
        
        // العلاقة مع الفئة
        public int CategoryId { get; set; }
        public virtual Category Category { get; set; }
        
        // العلاقة مع الوحدات
        public int UnitId { get; set; }
        public virtual Unit Unit { get; set; }
        
        // العلاقات مع الجداول الأخرى
        public virtual ICollection<ProductWarehouse> ProductWarehouses { get; set; }
        public virtual ICollection<InvoiceItem> InvoiceItems { get; set; }
    }
}
