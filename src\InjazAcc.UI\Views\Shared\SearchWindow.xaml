<Window x:Class="InjazAcc.UI.Views.Shared.SearchWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="البحث في النظام" 
        Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        FontFamily="{materialDesign:MaterialDesignFont}"
        FlowDirection="RightToLeft"
        WindowStyle="None"
        AllowsTransparency="True"
        BorderThickness="1"
        BorderBrush="{DynamicResource MaterialDesignDivider}"
        MouseDown="Window_MouseDown">

    <Window.Resources>
        <Style x:Key="SearchResultItemStyle" TargetType="ListViewItem">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F5F5F5"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Border CornerRadius="8" Background="White">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان -->
            <Grid Grid.Row="0" Margin="16,16,16,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="البحث في النظام" FontSize="18" FontWeight="SemiBold" VerticalAlignment="Center"/>

                <Button Grid.Column="1" Style="{StaticResource MaterialDesignIconButton}" Width="30" Height="30" Padding="0" Click="btnClose_Click">
                    <materialDesign:PackIcon Kind="Close" Width="18" Height="18"/>
                </Button>
            </Grid>

            <!-- مربع البحث -->
            <Grid Grid.Row="1" Margin="16,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox x:Name="txtSearch" Grid.Column="0" 
                         materialDesign:HintAssist.Hint="أدخل كلمة البحث..."
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Padding="12,8"
                         FontSize="14"
                         KeyDown="txtSearch_KeyDown"
                         TextChanged="txtSearch_TextChanged"/>

                <Button Grid.Column="1" Style="{StaticResource MaterialDesignFlatButton}" 
                        Margin="8,0,0,0" Padding="12,8" Click="btnSearch_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Magnify" Width="18" Height="18" VerticalAlignment="Center"/>
                        <TextBlock Text="بحث" Margin="8,0,0,0" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </Grid>

            <!-- نتائج البحث -->
            <Grid Grid.Row="2" Margin="16,8,16,16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان النتائج -->
                <TextBlock Grid.Row="0" Text="نتائج البحث" FontWeight="SemiBold" Margin="0,0,0,8"/>

                <!-- قائمة النتائج -->
                <ListView x:Name="lvSearchResults" Grid.Row="1" 
                          BorderThickness="1" BorderBrush="#EEEEEE"
                          ScrollViewer.VerticalScrollBarVisibility="Auto"
                          MouseDoubleClick="lvSearchResults_MouseDoubleClick">
                    <ListView.ItemTemplate>
                        <DataTemplate>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- أيقونة نوع النتيجة -->
                                <Border Grid.Column="0" Width="40" Height="40" Background="#F5F5F5" CornerRadius="20">
                                    <materialDesign:PackIcon Kind="{Binding IconKind}" Width="20" Height="20" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                                </Border>

                                <!-- تفاصيل النتيجة -->
                                <StackPanel Grid.Column="1" Margin="12,0,0,0">
                                    <TextBlock Text="{Binding Title}" FontWeight="SemiBold" FontSize="14"/>
                                    <TextBlock Text="{Binding Description}" Opacity="0.7" FontSize="12" Margin="0,4,0,0"/>
                                </StackPanel>

                                <!-- القسم -->
                                <Border Grid.Column="2" Background="#E0E0E0" CornerRadius="4" Padding="8,4" Margin="8,0,0,0">
                                    <TextBlock Text="{Binding Category}" FontSize="12"/>
                                </Border>
                            </Grid>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                    <ListView.ItemContainerStyle>
                        <Style BasedOn="{StaticResource SearchResultItemStyle}" TargetType="ListViewItem"/>
                    </ListView.ItemContainerStyle>
                </ListView>

                <!-- رسالة عدم وجود نتائج -->
                <TextBlock x:Name="txtNoResults" Grid.Row="1" 
                           Text="لا توجد نتائج للبحث" 
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Center"
                           FontSize="16" 
                           Opacity="0.5"
                           Visibility="Collapsed"/>
            </Grid>
        </Grid>
    </Border>
</Window>
