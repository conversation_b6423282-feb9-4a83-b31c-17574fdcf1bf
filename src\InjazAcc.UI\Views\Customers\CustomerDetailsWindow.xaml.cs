using System;
using System.Collections.ObjectModel;
using System.Windows;

namespace InjazAcc.UI.Views.Customers
{
    /// <summary>
    /// Interaction logic for CustomerDetailsWindow.xaml
    /// </summary>
    public partial class CustomerDetailsWindow : Window
    {
        private Customer _customer;
        private ObservableCollection<CustomerInvoice> _invoices;
        private ObservableCollection<CustomerPayment> _payments;
        private ObservableCollection<CustomerReturn> _returns;

        public CustomerDetailsWindow(Customer customer)
        {
            InitializeComponent();
            _customer = customer;
            LoadCustomerData();
            LoadSampleData();
        }

        private void LoadCustomerData()
        {
            // تحميل بيانات العميل
            if (_customer != null)
            {
                txtCustomerName.Text = $"تفاصيل العميل: {_customer.Name}";
                txtCode.Text = _customer.Code;
                txtName.Text = _customer.Name;
                txtPhone.Text = _customer.Phone;
                txtEmail.Text = _customer.Email;
                txtAddress.Text = _customer.Address;
                txtBalance.Text = _customer.Balance.ToString("N2") + " ر.س";
                
                // قيم افتراضية للعرض
                txtTotalSales.Text = "120,000.00 ر.س";
                txtLastTransaction.Text = DateTime.Now.AddDays(-5).ToString("yyyy-MM-dd");
            }
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية للفواتير
                _invoices = new ObservableCollection<CustomerInvoice>
                {
                    new CustomerInvoice { InvoiceNumber = "INV-001", Date = DateTime.Now.AddDays(-30), Amount = 5000, Paid = 5000, Remaining = 0, Status = "مدفوعة" },
                    new CustomerInvoice { InvoiceNumber = "INV-002", Date = DateTime.Now.AddDays(-25), Amount = 8000, Paid = 8000, Remaining = 0, Status = "مدفوعة" },
                    new CustomerInvoice { InvoiceNumber = "INV-003", Date = DateTime.Now.AddDays(-20), Amount = 12000, Paid = 6000, Remaining = 6000, Status = "جزئية" },
                    new CustomerInvoice { InvoiceNumber = "INV-004", Date = DateTime.Now.AddDays(-15), Amount = 7000, Paid = 0, Remaining = 7000, Status = "غير مدفوعة" },
                    new CustomerInvoice { InvoiceNumber = "INV-005", Date = DateTime.Now.AddDays(-5), Amount = 10000, Paid = 8000, Remaining = 2000, Status = "جزئية" }
                };
                dgInvoices.ItemsSource = _invoices;

                // بيانات تجريبية للمدفوعات
                _payments = new ObservableCollection<CustomerPayment>
                {
                    new CustomerPayment { ReceiptNumber = "REC-001", Date = DateTime.Now.AddDays(-28), Amount = 5000, PaymentMethod = "نقدي", Notes = "دفعة فاتورة INV-001" },
                    new CustomerPayment { ReceiptNumber = "REC-002", Date = DateTime.Now.AddDays(-23), Amount = 8000, PaymentMethod = "شيك", Notes = "دفعة فاتورة INV-002" },
                    new CustomerPayment { ReceiptNumber = "REC-003", Date = DateTime.Now.AddDays(-18), Amount = 6000, PaymentMethod = "تحويل بنكي", Notes = "دفعة جزئية فاتورة INV-003" },
                    new CustomerPayment { ReceiptNumber = "REC-004", Date = DateTime.Now.AddDays(-3), Amount = 8000, PaymentMethod = "نقدي", Notes = "دفعة جزئية فاتورة INV-005" }
                };
                dgPayments.ItemsSource = _payments;

                // بيانات تجريبية للمرتجعات
                _returns = new ObservableCollection<CustomerReturn>
                {
                    new CustomerReturn { ReturnNumber = "RET-001", Date = DateTime.Now.AddDays(-22), OriginalInvoiceNumber = "INV-002", Amount = 1500, Notes = "مرتجع جزئي - منتج معيب" },
                    new CustomerReturn { ReturnNumber = "RET-002", Date = DateTime.Now.AddDays(-10), OriginalInvoiceNumber = "INV-003", Amount = 2000, Notes = "مرتجع جزئي - خطأ في الطلب" }
                };
                dgReturns.ItemsSource = _returns;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnViewInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var invoice = (dgInvoices.SelectedItem as CustomerInvoice);
                if (invoice != null)
                {
                    MessageBox.Show($"عرض الفاتورة رقم: {invoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    // في التطبيق الحقيقي، سيتم فتح نافذة تفاصيل الفاتورة
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnViewReceipt_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var payment = (dgPayments.SelectedItem as CustomerPayment);
                if (payment != null)
                {
                    MessageBox.Show($"عرض سند القبض رقم: {payment.ReceiptNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    // في التطبيق الحقيقي، سيتم فتح نافذة تفاصيل سند القبض
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnViewReturn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var returnItem = (dgReturns.SelectedItem as CustomerReturn);
                if (returnItem != null)
                {
                    MessageBox.Show($"عرض المرتجع رقم: {returnItem.ReturnNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    // في التطبيق الحقيقي، سيتم فتح نافذة تفاصيل المرتجع
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEditCustomer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var customerWindow = new CustomerWindow(_customer);
                if (customerWindow.ShowDialog() == true)
                {
                    _customer = customerWindow.Customer;
                    LoadCustomerData();
                    MessageBox.Show("تم تعديل بيانات العميل بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintStatement_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري طباعة كشف حساب العميل...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                // في التطبيق الحقيقي، سيتم طباعة كشف حساب العميل
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    public class CustomerInvoice
    {
        public string InvoiceNumber { get; set; }
        public DateTime Date { get; set; }
        public double Amount { get; set; }
        public double Paid { get; set; }
        public double Remaining { get; set; }
        public string Status { get; set; }
    }

    public class CustomerPayment
    {
        public string ReceiptNumber { get; set; }
        public DateTime Date { get; set; }
        public double Amount { get; set; }
        public string PaymentMethod { get; set; }
        public string Notes { get; set; }
    }

    public class CustomerReturn
    {
        public string ReturnNumber { get; set; }
        public DateTime Date { get; set; }
        public string OriginalInvoiceNumber { get; set; }
        public double Amount { get; set; }
        public string Notes { get; set; }
    }
}
