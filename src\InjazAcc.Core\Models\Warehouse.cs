using System.Collections.Generic;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج المخزن في النظام
    /// </summary>
    public class Warehouse
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Location { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        
        // العلاقة مع المنتجات
        public virtual ICollection<ProductWarehouse> ProductWarehouses { get; set; }
        
        // العلاقة مع عمليات التحويل المخزني
        public virtual ICollection<InventoryTransfer> SourceTransfers { get; set; }
        public virtual ICollection<InventoryTransfer> DestinationTransfers { get; set; }
    }
}
