namespace InjazAcc.Core.Exceptions
{
    /// <summary>
    /// فئة تحتوي على ثوابت لرموز الأخطاء المستخدمة في التطبيق
    /// </summary>
    public static class ErrorCodes
    {
        // رموز الأخطاء العامة (ERR-GEN-XXX)
        public const string GeneralError = "ERR-GEN-001";
        public const string UnexpectedError = "ERR-GEN-002";
        public const string ConfigurationError = "ERR-GEN-003";

        // رموز أخطاء قاعدة البيانات (ERR-DB-XXX)
        public const string DatabaseConnectionError = "ERR-DB-001";
        public const string DatabaseQueryError = "ERR-DB-002";
        public const string DatabaseUpdateError = "ERR-DB-003";
        public const string DatabaseInsertError = "ERR-DB-004";
        public const string DatabaseDeleteError = "ERR-DB-005";
        public const string DatabaseMigrationError = "ERR-DB-006";

        // رموز أخطاء المصادقة (ERR-AUTH-XXX)
        public const string AuthenticationFailed = "ERR-AUTH-001";
        public const string InvalidCredentials = "ERR-AUTH-002";
        public const string AccountLocked = "ERR-AUTH-003";
        public const string PasswordExpired = "ERR-AUTH-004";

        // رموز أخطاء التفويض (ERR-AUTHZ-XXX)
        public const string UnauthorizedAccess = "ERR-AUTHZ-001";
        public const string InsufficientPermissions = "ERR-AUTHZ-002";
        public const string ResourceAccessDenied = "ERR-AUTHZ-003";

        // رموز أخطاء التحقق من صحة البيانات (ERR-VAL-XXX)
        public const string ValidationFailed = "ERR-VAL-001";
        public const string RequiredValueMissing = "ERR-VAL-002";
        public const string InvalidStringFormat = "ERR-VAL-003";
        public const string ValueOutOfRange = "ERR-VAL-004";
        public const string ConditionNotMet = "ERR-VAL-005";
        public const string CollectionEmpty = "ERR-VAL-006";
        public const string DuplicateValue = "ERR-VAL-007";
        public const string InvalidDateRange = "ERR-VAL-008";
        public const string InvalidEmailFormat = "ERR-VAL-009";
        public const string InvalidPhoneFormat = "ERR-VAL-010";
        public const string InvalidValue = "ERR-VAL-011";

        // رموز أخطاء العمليات التجارية (ERR-BUS-XXX)
        public const string BusinessRuleViolation = "ERR-BUS-001";
        public const string InsufficientFunds = "ERR-BUS-002";
        public const string InsufficientInventory = "ERR-BUS-003";
        public const string DuplicateTransaction = "ERR-BUS-004";
        public const string InvalidTransactionStatus = "ERR-BUS-005";
        public const string InvalidAccountingPeriod = "ERR-BUS-006";
        public const string AccountingPeriodClosed = "ERR-BUS-007";

        // رموز أخطاء الملفات (ERR-FILE-XXX)
        public const string FileNotFound = "ERR-FILE-001";
        public const string FileAccessDenied = "ERR-FILE-002";
        public const string InvalidFileFormat = "ERR-FILE-003";
        public const string FileTooLarge = "ERR-FILE-004";

        // رموز أخطاء الشبكة (ERR-NET-XXX)
        public const string NetworkConnectionError = "ERR-NET-001";
        public const string ServerNotResponding = "ERR-NET-002";
        public const string TimeoutError = "ERR-NET-003";
    }
}
