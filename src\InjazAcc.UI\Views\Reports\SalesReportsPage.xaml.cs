using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace InjazAcc.UI.Views.Reports
{
    /// <summary>
    /// Interaction logic for SalesReportsPage.xaml
    /// </summary>
    public partial class SalesReportsPage : Page
    {
        private ObservableCollection<SalesReportItem> _reportItems;

        public SalesReportsPage()
        {
            try
            {
                InitializeComponent();

                // تعيين التواريخ الافتراضية (الشهر الحالي)
                if (dpFromDate != null && dpToDate != null)
                {
                    dpFromDate.SelectedDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                    dpToDate.SelectedDate = DateTime.Now;
                }

                // تهيئة البيانات
                _reportItems = new ObservableCollection<SalesReportItem>();
                if (dgReport != null)
                {
                    dgReport.ItemsSource = _reportItems;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة صفحة تقارير المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void cmbReportType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // تغيير أعمدة الجدول حسب نوع التقرير
                switch (cmbReportType.SelectedIndex)
                {
                    case 0: // تقرير المبيعات اليومي
                    case 1: // تقرير المبيعات الشهري
                        ConfigureDailySalesReportColumns();
                        break;
                    case 2: // تقرير المبيعات حسب العميل
                        ConfigureCustomerSalesReportColumns();
                        break;
                    case 3: // تقرير المبيعات حسب الصنف
                        ConfigureItemSalesReportColumns();
                        break;
                    case 4: // تقرير مرتجعات المبيعات
                        ConfigureSalesReturnsReportColumns();
                        break;
                    case 5: // تقرير تحليل المبيعات
                        ConfigureSalesAnalysisReportColumns();
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ConfigureDailySalesReportColumns()
        {
            dgReport.Columns.Clear();

            dgReport.Columns.Add(new DataGridTextColumn { Header = "رقم الفاتورة", Binding = new System.Windows.Data.Binding("InvoiceNumber"), Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "التاريخ", Binding = new System.Windows.Data.Binding("Date") { StringFormat = "yyyy-MM-dd" }, Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "العميل", Binding = new System.Windows.Data.Binding("CustomerName"), Width = 150 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي الفاتورة", Binding = new System.Windows.Data.Binding("Total") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الخصم", Binding = new System.Windows.Data.Binding("Discount") { StringFormat = "N2" }, Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الضريبة", Binding = new System.Windows.Data.Binding("Tax") { StringFormat = "N2" }, Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الصافي", Binding = new System.Windows.Data.Binding("Net") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الحالة", Binding = new System.Windows.Data.Binding("Status"), Width = 100 });
        }

        private void ConfigureCustomerSalesReportColumns()
        {
            dgReport.Columns.Clear();

            dgReport.Columns.Add(new DataGridTextColumn { Header = "كود العميل", Binding = new System.Windows.Data.Binding("CustomerCode"), Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "اسم العميل", Binding = new System.Windows.Data.Binding("CustomerName"), Width = 150 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "عدد الفواتير", Binding = new System.Windows.Data.Binding("InvoiceCount"), Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي المبيعات", Binding = new System.Windows.Data.Binding("Total") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي الخصومات", Binding = new System.Windows.Data.Binding("Discount") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي الضرائب", Binding = new System.Windows.Data.Binding("Tax") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الصافي", Binding = new System.Windows.Data.Binding("Net") { StringFormat = "N2" }, Width = 120 });
        }

        private void ConfigureItemSalesReportColumns()
        {
            dgReport.Columns.Clear();

            dgReport.Columns.Add(new DataGridTextColumn { Header = "كود الصنف", Binding = new System.Windows.Data.Binding("ItemCode"), Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "اسم الصنف", Binding = new System.Windows.Data.Binding("ItemName"), Width = 150 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الكمية المباعة", Binding = new System.Windows.Data.Binding("Quantity") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الوحدة", Binding = new System.Windows.Data.Binding("Unit"), Width = 80 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "متوسط سعر البيع", Binding = new System.Windows.Data.Binding("AveragePrice") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي المبيعات", Binding = new System.Windows.Data.Binding("Total") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "نسبة من إجمالي المبيعات", Binding = new System.Windows.Data.Binding("Percentage") { StringFormat = "P2" }, Width = 150 });
        }

        private void ConfigureSalesReturnsReportColumns()
        {
            dgReport.Columns.Clear();

            dgReport.Columns.Add(new DataGridTextColumn { Header = "رقم المرتجع", Binding = new System.Windows.Data.Binding("ReturnNumber"), Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "التاريخ", Binding = new System.Windows.Data.Binding("Date") { StringFormat = "yyyy-MM-dd" }, Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "رقم الفاتورة الأصلية", Binding = new System.Windows.Data.Binding("OriginalInvoiceNumber"), Width = 150 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "العميل", Binding = new System.Windows.Data.Binding("CustomerName"), Width = 150 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي المرتجع", Binding = new System.Windows.Data.Binding("Total") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الخصم", Binding = new System.Windows.Data.Binding("Discount") { StringFormat = "N2" }, Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الضريبة", Binding = new System.Windows.Data.Binding("Tax") { StringFormat = "N2" }, Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الصافي", Binding = new System.Windows.Data.Binding("Net") { StringFormat = "N2" }, Width = 120 });
        }

        private void ConfigureSalesAnalysisReportColumns()
        {
            dgReport.Columns.Clear();

            dgReport.Columns.Add(new DataGridTextColumn { Header = "الفترة", Binding = new System.Windows.Data.Binding("Period"), Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "عدد الفواتير", Binding = new System.Windows.Data.Binding("InvoiceCount"), Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي المبيعات", Binding = new System.Windows.Data.Binding("Total") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "متوسط قيمة الفاتورة", Binding = new System.Windows.Data.Binding("AverageInvoice") { StringFormat = "N2" }, Width = 150 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "نسبة النمو", Binding = new System.Windows.Data.Binding("GrowthRate") { StringFormat = "P2" }, Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "عدد العملاء", Binding = new System.Windows.Data.Binding("CustomerCount"), Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "عدد الأصناف المباعة", Binding = new System.Windows.Data.Binding("ItemCount"), Width = 150 });
        }

        private void btnShowReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحميل بيانات تجريبية
                LoadSampleData();

                // حساب الإجماليات
                CalculateTotals();

                MessageBox.Show("تم تحميل التقرير بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadSampleData()
        {
            _reportItems.Clear();

            // بيانات تجريبية حسب نوع التقرير
            switch (cmbReportType.SelectedIndex)
            {
                case 0: // تقرير المبيعات اليومي
                case 1: // تقرير المبيعات الشهري
                    LoadDailySalesReportData();
                    break;
                case 2: // تقرير المبيعات حسب العميل
                    LoadCustomerSalesReportData();
                    break;
                case 3: // تقرير المبيعات حسب الصنف
                    LoadItemSalesReportData();
                    break;
                case 4: // تقرير مرتجعات المبيعات
                    LoadSalesReturnsReportData();
                    break;
                case 5: // تقرير تحليل المبيعات
                    LoadSalesAnalysisReportData();
                    break;
            }
        }

        private void LoadDailySalesReportData()
        {
            // بيانات تجريبية لتقرير المبيعات اليومي
            _reportItems.Add(new SalesReportItem { InvoiceNumber = "INV-001", Date = DateTime.Now.AddDays(-5), CustomerName = "شركة الأمل للتجارة", Total = 5000, Discount = 250, Tax = 237.5, Net = 4987.5, Status = "مكتملة" });
            _reportItems.Add(new SalesReportItem { InvoiceNumber = "INV-002", Date = DateTime.Now.AddDays(-4), CustomerName = "مؤسسة النور", Total = 3500, Discount = 0, Tax = 175, Net = 3675, Status = "مكتملة" });
            _reportItems.Add(new SalesReportItem { InvoiceNumber = "INV-003", Date = DateTime.Now.AddDays(-3), CustomerName = "شركة الصفا للمقاولات", Total = 8000, Discount = 400, Tax = 380, Net = 7980, Status = "مكتملة" });
            _reportItems.Add(new SalesReportItem { InvoiceNumber = "INV-004", Date = DateTime.Now.AddDays(-2), CustomerName = "مؤسسة الإبداع", Total = 2500, Discount = 125, Tax = 118.75, Net = 2493.75, Status = "مكتملة" });
            _reportItems.Add(new SalesReportItem { InvoiceNumber = "INV-005", Date = DateTime.Now.AddDays(-1), CustomerName = "شركة المستقبل", Total = 6000, Discount = 300, Tax = 285, Net = 5985, Status = "مكتملة" });
        }

        private void LoadCustomerSalesReportData()
        {
            // بيانات تجريبية لتقرير المبيعات حسب العميل
            _reportItems.Add(new SalesReportItem { CustomerCode = "C001", CustomerName = "شركة الأمل للتجارة", InvoiceCount = 5, Total = 25000, Discount = 1250, Tax = 1187.5, Net = 24937.5 });
            _reportItems.Add(new SalesReportItem { CustomerCode = "C002", CustomerName = "مؤسسة النور", InvoiceCount = 3, Total = 10500, Discount = 0, Tax = 525, Net = 11025 });
            _reportItems.Add(new SalesReportItem { CustomerCode = "C003", CustomerName = "شركة الصفا للمقاولات", InvoiceCount = 4, Total = 32000, Discount = 1600, Tax = 1520, Net = 31920 });
            _reportItems.Add(new SalesReportItem { CustomerCode = "C004", CustomerName = "مؤسسة الإبداع", InvoiceCount = 2, Total = 5000, Discount = 250, Tax = 237.5, Net = 4987.5 });
            _reportItems.Add(new SalesReportItem { CustomerCode = "C005", CustomerName = "شركة المستقبل", InvoiceCount = 3, Total = 18000, Discount = 900, Tax = 855, Net = 17955 });
        }

        private void LoadItemSalesReportData()
        {
            // بيانات تجريبية لتقرير المبيعات حسب الصنف
            _reportItems.Add(new SalesReportItem { ItemCode = "I001", ItemName = "جهاز كمبيوتر محمول", Quantity = 10, Unit = "قطعة", AveragePrice = 3500, Total = 35000, Percentage = 0.35 });
            _reportItems.Add(new SalesReportItem { ItemCode = "I002", ItemName = "طابعة ليزر", Quantity = 5, Unit = "قطعة", AveragePrice = 1200, Total = 6000, Percentage = 0.06 });
            _reportItems.Add(new SalesReportItem { ItemCode = "I003", ItemName = "شاشة كمبيوتر", Quantity = 15, Unit = "قطعة", AveragePrice = 800, Total = 12000, Percentage = 0.12 });
            _reportItems.Add(new SalesReportItem { ItemCode = "I004", ItemName = "لوحة مفاتيح", Quantity = 20, Unit = "قطعة", AveragePrice = 150, Total = 3000, Percentage = 0.03 });
            _reportItems.Add(new SalesReportItem { ItemCode = "I005", ItemName = "ماوس لاسلكي", Quantity = 25, Unit = "قطعة", AveragePrice = 100, Total = 2500, Percentage = 0.025 });
        }

        private void LoadSalesReturnsReportData()
        {
            // بيانات تجريبية لتقرير مرتجعات المبيعات
            _reportItems.Add(new SalesReportItem { ReturnNumber = "RET-001", Date = DateTime.Now.AddDays(-4), OriginalInvoiceNumber = "INV-001", CustomerName = "شركة الأمل للتجارة", Total = 1000, Discount = 50, Tax = 47.5, Net = 997.5 });
            _reportItems.Add(new SalesReportItem { ReturnNumber = "RET-002", Date = DateTime.Now.AddDays(-3), OriginalInvoiceNumber = "INV-003", CustomerName = "شركة الصفا للمقاولات", Total = 2000, Discount = 100, Tax = 95, Net = 1995 });
            _reportItems.Add(new SalesReportItem { ReturnNumber = "RET-003", Date = DateTime.Now.AddDays(-2), OriginalInvoiceNumber = "INV-005", CustomerName = "شركة المستقبل", Total = 1500, Discount = 75, Tax = 71.25, Net = 1496.25 });
        }

        private void LoadSalesAnalysisReportData()
        {
            // بيانات تجريبية لتقرير تحليل المبيعات
            _reportItems.Add(new SalesReportItem { Period = "يناير 2023", InvoiceCount = 45, Total = 90000, AverageInvoice = 2000, GrowthRate = 0, CustomerCount = 15, ItemCount = 25 });
            _reportItems.Add(new SalesReportItem { Period = "فبراير 2023", InvoiceCount = 50, Total = 100000, AverageInvoice = 2000, GrowthRate = 0.11, CustomerCount = 18, ItemCount = 28 });
            _reportItems.Add(new SalesReportItem { Period = "مارس 2023", InvoiceCount = 55, Total = 110000, AverageInvoice = 2000, GrowthRate = 0.1, CustomerCount = 20, ItemCount = 30 });
            _reportItems.Add(new SalesReportItem { Period = "أبريل 2023", InvoiceCount = 60, Total = 120000, AverageInvoice = 2000, GrowthRate = 0.09, CustomerCount = 22, ItemCount = 32 });
            _reportItems.Add(new SalesReportItem { Period = "مايو 2023", InvoiceCount = 65, Total = 130000, AverageInvoice = 2000, GrowthRate = 0.08, CustomerCount = 25, ItemCount = 35 });
        }

        private void CalculateTotals()
        {
            double totalSales = 0;
            double totalDiscounts = 0;
            double totalTaxes = 0;
            double totalNet = 0;

            foreach (var item in _reportItems)
            {
                if (cmbReportType.SelectedIndex <= 2) // تقارير المبيعات اليومي والشهري وحسب العميل
                {
                    totalSales += item.Total;
                    totalDiscounts += item.Discount;
                    totalTaxes += item.Tax;
                    totalNet += item.Net;
                }
                else if (cmbReportType.SelectedIndex == 3) // تقرير المبيعات حسب الصنف
                {
                    totalSales += item.Total;
                }
                else if (cmbReportType.SelectedIndex == 4) // تقرير مرتجعات المبيعات
                {
                    totalSales += item.Total;
                    totalDiscounts += item.Discount;
                    totalTaxes += item.Tax;
                    totalNet += item.Net;
                }
            }

            txtTotalSales.Text = totalSales.ToString("N2");
            txtTotalDiscounts.Text = totalDiscounts.ToString("N2");
            txtTotalTaxes.Text = totalTaxes.ToString("N2");
            txtTotalNet.Text = totalNet.ToString("N2");
        }

        private void btnPrintReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري طباعة التقرير...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnExportExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري تصدير التقرير إلى Excel...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnExportPDF_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري تصدير التقرير إلى PDF...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnBackToReports_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // العودة إلى صفحة التقارير الرئيسية
                if (NavigationService != null)
                {
                    NavigationService.Navigate(new ReportsPage());
                }
                else
                {
                    MessageBox.Show("لا يمكن العودة إلى صفحة التقارير", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء العودة لصفحة التقارير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class SalesReportItem
    {
        // خصائص تقرير المبيعات اليومي/الشهري
        public string InvoiceNumber { get; set; } = "";
        public DateTime Date { get; set; }
        public string CustomerName { get; set; } = "";
        public double Total { get; set; }
        public double Discount { get; set; }
        public double Tax { get; set; }
        public double Net { get; set; }
        public string Status { get; set; } = "";

        // خصائص تقرير المبيعات حسب العميل
        public string CustomerCode { get; set; } = "";
        public int InvoiceCount { get; set; }

        // خصائص تقرير المبيعات حسب الصنف
        public string ItemCode { get; set; } = "";
        public string ItemName { get; set; } = "";
        public double Quantity { get; set; }
        public string Unit { get; set; } = "";
        public double AveragePrice { get; set; }
        public double Percentage { get; set; }

        // خصائص تقرير مرتجعات المبيعات
        public string ReturnNumber { get; set; } = "";
        public string OriginalInvoiceNumber { get; set; } = "";

        // خصائص تقرير تحليل المبيعات
        public string Period { get; set; } = "";
        public double AverageInvoice { get; set; }
        public double GrowthRate { get; set; }
        public int CustomerCount { get; set; }
        public int ItemCount { get; set; }
    }
}
