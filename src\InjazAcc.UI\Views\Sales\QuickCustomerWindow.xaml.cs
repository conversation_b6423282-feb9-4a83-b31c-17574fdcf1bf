using System;
using System.Windows;

namespace InjazAcc.UI.Views.Sales
{
    /// <summary>
    /// Interaction logic for QuickCustomerWindow.xaml
    /// </summary>
    public partial class QuickCustomerWindow : Window
    {
        private CustomerInfo _customer;

        public CustomerInfo Customer => _customer;

        public QuickCustomerWindow()
        {
            InitializeComponent();
            _customer = new CustomerInfo();
            InitializeControls();
        }

        private void InitializeControls()
        {
            // تعيين القيم الافتراضية
            txtCode.Text = GenerateNewCode();
        }

        private string GenerateNewCode()
        {
            // توليد رمز جديد (في التطبيق الحقيقي سيتم جلب الرمز من قاعدة البيانات)
            Random random = new Random();
            return $"C{random.Next(1000, 9999)}";
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم العميل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtName.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPhone.Text))
                {
                    MessageBox.Show("الرجاء إدخال رقم هاتف العميل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtPhone.Focus();
                    return;
                }

                // تحديث بيانات العميل
                _customer.Code = txtCode.Text;
                _customer.Name = txtName.Text;
                _customer.Phone = txtPhone.Text;
                _customer.Email = txtEmail.Text;
                _customer.Address = txtAddress.Text;
                _customer.Notes = txtNotes.Text;

                // إغلاق النافذة بنجاح
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إلغاء العملية وإغلاق النافذة
            this.DialogResult = false;
        }
    }

    /// <summary>
    /// نموذج بيانات العميل المبسط للاستخدام في واجهة المستخدم
    /// </summary>
    public class CustomerInfo
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string Notes { get; set; }
    }
}
