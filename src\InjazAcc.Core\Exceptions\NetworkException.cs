using System;

namespace InjazAcc.Core.Exceptions
{
    /// <summary>
    /// استثناء مخصص لأخطاء الشبكة
    /// </summary>
    public class NetworkException : InjazAccException
    {
        /// <summary>
        /// عنوان URL المرتبط بالاستثناء
        /// </summary>
        public string Url { get; }

        /// <summary>
        /// إنشاء استثناء جديد بدون رسالة
        /// </summary>
        public NetworkException() : base("حدث خطأ أثناء الاتصال بالشبكة", ErrorCodes.NetworkConnectionError)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        public NetworkException(string message) : base(message, ErrorCodes.NetworkConnectionError)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة وعنوان URL
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="url">عنوان URL</param>
        /// <remarks>
        /// تم تعطيل هذا البناء لتجنب التعارض مع البناء الآخر الذي يأخذ نفس أنواع المعلمات
        /// استخدم البناء الذي يأخذ رمز خطأ بدلاً من ذلك
        /// </remarks>
        //public NetworkException(string message, string url) : base(message, ErrorCodes.NetworkConnectionError)
        //{
        //    Url = url;
        //}

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        public NetworkException(string message, string errorCode) : base(message, errorCode)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ وعنوان URL
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        /// <param name="url">عنوان URL</param>
        public NetworkException(string message, string errorCode, string url) : base(message, errorCode)
        {
            Url = url;
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public NetworkException(string message, Exception innerException) : base(message, ErrorCodes.NetworkConnectionError, innerException)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة وعنوان URL واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="url">عنوان URL</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        /// <remarks>
        /// تم تعطيل هذا البناء لتجنب التعارض مع البناء الآخر الذي يأخذ نفس أنواع المعلمات
        /// استخدم البناء الذي يأخذ رمز خطأ بدلاً من ذلك
        /// </remarks>
        //public NetworkException(string message, string url, Exception innerException) : base(message, ErrorCodes.NetworkConnectionError, innerException)
        //{
        //    Url = url;
        //}

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public NetworkException(string message, string errorCode, Exception innerException) : base(message, errorCode, innerException)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ وعنوان URL واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        /// <param name="url">عنوان URL</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public NetworkException(string message, string errorCode, string url, Exception innerException) : base(message, errorCode, innerException)
        {
            Url = url;
        }

        /// <summary>
        /// الحصول على رسالة خطأ مفصلة تتضمن عنوان URL
        /// </summary>
        /// <returns>رسالة الخطأ المفصلة</returns>
        public override string GetDetailedMessage()
        {
            var baseMessage = base.GetDetailedMessage();

            if (string.IsNullOrEmpty(Url))
            {
                return baseMessage;
            }

            return $"{baseMessage}\nعنوان URL: {Url}";
        }
    }
}
