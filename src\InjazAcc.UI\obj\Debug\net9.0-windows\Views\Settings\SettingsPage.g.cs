﻿#pragma checksum "..\..\..\..\..\Views\Settings\SettingsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "50A6BCDFFDC46C264831B56C227310817C4CE2FC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using InjazAcc.UI.Views.Settings;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InjazAcc.UI.Views.Settings {
    
    
    /// <summary>
    /// SettingsPage
    /// </summary>
    public partial class SettingsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 61 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtCompanyName;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtLegalName;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtTaxNumber;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtCommercialRegister;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpEstablishmentDate;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtAddress;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtPhone;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtEmail;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtWebsite;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtLogoPath;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbCurrency;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtCurrencySymbol;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSaveCompanyInfo;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkAutoBackup;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkShowWelcomeScreen;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkAutoUpdate;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkSendNotifications;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSalesInvoicePrefix;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtPurchaseInvoicePrefix;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtDefaultTaxRate;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtDefaultPaymentPeriod;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbInvoicePrinter;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnRefreshInvoicePrinters;
        
        #line default
        #line hidden
        
        
        #line 281 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbReportPrinter;
        
        #line default
        #line hidden
        
        
        #line 282 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnRefreshReportPrinters;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbInvoicePaperSize;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbReportPaperSize;
        
        #line default
        #line hidden
        
        
        #line 313 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkPrintLogo;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkPrintPreview;
        
        #line default
        #line hidden
        
        
        #line 315 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkAutoPrint;
        
        #line default
        #line hidden
        
        
        #line 323 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSaveGeneralSettings;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnResetSettings;
        
        #line default
        #line hidden
        
        
        #line 359 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSearchUser;
        
        #line default
        #line hidden
        
        
        #line 363 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddUser;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgUsers;
        
        #line default
        #line hidden
        
        
        #line 413 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalUsers;
        
        #line default
        #line hidden
        
        
        #line 421 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtActiveUsers;
        
        #line default
        #line hidden
        
        
        #line 429 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtInactiveUsers;
        
        #line default
        #line hidden
        
        
        #line 452 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSearchRole;
        
        #line default
        #line hidden
        
        
        #line 456 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddRole;
        
        #line default
        #line hidden
        
        
        #line 461 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgRoles;
        
        #line default
        #line hidden
        
        
        #line 500 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalRoles;
        
        #line default
        #line hidden
        
        
        #line 515 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSearchPermission;
        
        #line default
        #line hidden
        
        
        #line 520 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgPermissions;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InjazAcc.UI;component/views/settings/settingspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.txtCompanyName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.txtLegalName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.txtTaxNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.txtCommercialRegister = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.dpEstablishmentDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 6:
            this.txtAddress = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.txtPhone = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.txtEmail = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.txtWebsite = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.txtLogoPath = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            
            #line 113 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnBrowseLogo_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.cmbCurrency = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.txtCurrencySymbol = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.btnSaveCompanyInfo = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnSaveCompanyInfo.Click += new System.Windows.RoutedEventHandler(this.btnSaveCompanyInfo_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.chkAutoBackup = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.chkShowWelcomeScreen = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.chkAutoUpdate = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 18:
            this.chkSendNotifications = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 19:
            this.txtSalesInvoicePrefix = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.txtPurchaseInvoicePrefix = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.txtDefaultTaxRate = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.txtDefaultPaymentPeriod = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.cmbInvoicePrinter = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 24:
            this.btnRefreshInvoicePrinters = ((System.Windows.Controls.Button)(target));
            
            #line 267 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnRefreshInvoicePrinters.Click += new System.Windows.RoutedEventHandler(this.btnRefreshPrinters_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.cmbReportPrinter = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 26:
            this.btnRefreshReportPrinters = ((System.Windows.Controls.Button)(target));
            
            #line 283 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnRefreshReportPrinters.Click += new System.Windows.RoutedEventHandler(this.btnRefreshPrinters_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.cmbInvoicePaperSize = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 28:
            this.cmbReportPaperSize = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 29:
            this.chkPrintLogo = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 30:
            this.chkPrintPreview = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 31:
            this.chkAutoPrint = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 32:
            this.btnSaveGeneralSettings = ((System.Windows.Controls.Button)(target));
            
            #line 323 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnSaveGeneralSettings.Click += new System.Windows.RoutedEventHandler(this.btnSaveGeneralSettings_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.btnResetSettings = ((System.Windows.Controls.Button)(target));
            
            #line 324 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnResetSettings.Click += new System.Windows.RoutedEventHandler(this.btnResetSettings_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            this.txtSearchUser = ((System.Windows.Controls.TextBox)(target));
            
            #line 361 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.txtSearchUser.KeyUp += new System.Windows.Input.KeyEventHandler(this.txtSearchUser_KeyUp);
            
            #line default
            #line hidden
            return;
            case 35:
            this.btnAddUser = ((System.Windows.Controls.Button)(target));
            
            #line 364 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnAddUser.Click += new System.Windows.RoutedEventHandler(this.btnAddUser_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.dgUsers = ((System.Windows.Controls.DataGrid)(target));
            
            #line 372 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.dgUsers.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.dgUsers_MouseDoubleClick);
            
            #line default
            #line hidden
            
            #line 373 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.dgUsers.CellEditEnding += new System.EventHandler<System.Windows.Controls.DataGridCellEditEndingEventArgs>(this.dgUsers_CellEditEnding);
            
            #line default
            #line hidden
            return;
            case 39:
            this.txtTotalUsers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 40:
            this.txtActiveUsers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 41:
            this.txtInactiveUsers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 42:
            this.txtSearchRole = ((System.Windows.Controls.TextBox)(target));
            
            #line 454 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.txtSearchRole.KeyUp += new System.Windows.Input.KeyEventHandler(this.txtSearchRole_KeyUp);
            
            #line default
            #line hidden
            return;
            case 43:
            this.btnAddRole = ((System.Windows.Controls.Button)(target));
            
            #line 457 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnAddRole.Click += new System.Windows.RoutedEventHandler(this.btnAddRole_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            this.dgRoles = ((System.Windows.Controls.DataGrid)(target));
            
            #line 465 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.dgRoles.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.dgRoles_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 48:
            this.txtTotalRoles = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 49:
            this.txtSearchPermission = ((System.Windows.Controls.TextBox)(target));
            
            #line 517 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.txtSearchPermission.KeyUp += new System.Windows.Input.KeyEventHandler(this.txtSearchPermission_KeyUp);
            
            #line default
            #line hidden
            return;
            case 50:
            this.dgPermissions = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 37:
            
            #line 386 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnEditUser_Click);
            
            #line default
            #line hidden
            break;
            case 38:
            
            #line 391 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnDeleteUser_Click);
            
            #line default
            #line hidden
            break;
            case 45:
            
            #line 475 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnEditRole_Click);
            
            #line default
            #line hidden
            break;
            case 46:
            
            #line 480 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnManagePermissions_Click);
            
            #line default
            #line hidden
            break;
            case 47:
            
            #line 485 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnDeleteRole_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

