<Window x:Class="InjazAcc.UI.Views.Accounts.AccountWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Accounts"
        mc:Ignorable="d"
        Title="إضافة حساب جديد" Height="400" Width="500"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        TextElement.FontSize="14"
        TextElement.FontWeight="Regular"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- عنوان النافذة -->
        <TextBlock Grid.Row="0" x:Name="txtWindowTitle" Text="إضافة حساب جديد" Style="{StaticResource PageTitle}" HorizontalAlignment="Center" Margin="0,0,0,20"/>
        
        <!-- نموذج إدخال البيانات -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- رقم الحساب -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم الحساب:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtAccountNumber" Margin="0,5"/>
            
            <!-- اسم الحساب -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="اسم الحساب:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtAccountName" Margin="0,5"/>
            
            <!-- نوع الحساب -->
            <TextBlock Grid.Row="2" Grid.Column="0" Text="نوع الحساب:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <ComboBox Grid.Row="2" Grid.Column="1" x:Name="cmbAccountType" Margin="0,5">
                <ComboBoxItem Content="الأصول"/>
                <ComboBoxItem Content="الخصوم"/>
                <ComboBoxItem Content="حقوق الملكية"/>
                <ComboBoxItem Content="الإيرادات"/>
                <ComboBoxItem Content="المصروفات"/>
            </ComboBox>
            
            <!-- المستوى -->
            <TextBlock Grid.Row="3" Grid.Column="0" Text="المستوى:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <ComboBox Grid.Row="3" Grid.Column="1" x:Name="cmbLevel" Margin="0,5" SelectionChanged="cmbLevel_SelectionChanged">
                <ComboBoxItem Content="1"/>
                <ComboBoxItem Content="2"/>
                <ComboBoxItem Content="3"/>
                <ComboBoxItem Content="4"/>
            </ComboBox>
            
            <!-- الحساب الرئيسي -->
            <TextBlock Grid.Row="4" Grid.Column="0" Text="الحساب الرئيسي:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <ComboBox Grid.Row="4" Grid.Column="1" x:Name="cmbParentAccount" Margin="0,5" IsEnabled="False"/>
            
            <!-- الوصف -->
            <TextBlock Grid.Row="5" Grid.Column="0" Text="الوصف:" Margin="0,0,10,0" VerticalAlignment="Top"/>
            <TextBox Grid.Row="5" Grid.Column="1" x:Name="txtDescription" TextWrapping="Wrap" AcceptsReturn="True" Height="80" Margin="0,5"/>
        </Grid>
        
        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="حفظ" Margin="5,0" Click="btnSave_Click"/>
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="إلغاء" Margin="5,0" Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
