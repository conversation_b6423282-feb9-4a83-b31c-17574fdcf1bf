using System;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج المدفوعات في النظام
    /// </summary>
    public class Payment
    {
        public int Id { get; set; }
        public string ReferenceNumber { get; set; }
        public DateTime PaymentDate { get; set; }
        public PaymentType Type { get; set; }
        public PaymentMethod Method { get; set; }
        public decimal Amount { get; set; }
        public string Notes { get; set; }
        
        // العلاقة مع الفاتورة (إن وجدت)
        public int? InvoiceId { get; set; }
        public virtual Invoice Invoice { get; set; }
        
        // العلاقة مع العميل/المورد
        public int? CustomerId { get; set; }
        public virtual Customer Customer { get; set; }
        
        public int? SupplierId { get; set; }
        public virtual Supplier Supplier { get; set; }
        
        // العلاقة مع المستخدم الذي سجل الدفعة
        public int UserId { get; set; }
        public virtual User User { get; set; }
        
        // العلاقة مع الحساب المالي
        public int AccountId { get; set; }
        public virtual Account Account { get; set; }
    }
    
    /// <summary>
    /// أنواع المدفوعات
    /// </summary>
    public enum PaymentType
    {
        CustomerPayment = 1,     // دفعة من عميل
        SupplierPayment = 2,     // دفعة لمورد
        ExpensePayment = 3,      // دفعة مصروفات
        RevenuePayment = 4       // دفعة إيرادات
    }
    
    /// <summary>
    /// طرق الدفع
    /// </summary>
    public enum PaymentMethod
    {
        Cash = 1,            // نقداً
        BankTransfer = 2,    // تحويل بنكي
        Check = 3,           // شيك
        CreditCard = 4,      // بطاقة ائتمان
        Other = 5            // أخرى
    }
}
