﻿using System;
using System.Configuration;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using InjazAcc.Core.Interfaces;
using InjazAcc.DataAccess;
using InjazAcc.Services;

namespace InjazAcc.UI;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    // حاوية الاعتمادية
    private static IServiceProvider ServiceProvider;

    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // تهيئة حاوية الاعتمادية
        ConfigureServices();

        // إضافة معالج للاستثناءات غير المعالجة
        AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
        this.DispatcherUnhandledException += App_DispatcherUnhandledException;

        // إضافة معالج حدث إنهاء التطبيق
        this.Exit += App_Exit;
    }

    /// <summary>
    /// تهيئة حاوية الاعتمادية وتسجيل الخدمات
    /// </summary>
    private void ConfigureServices()
    {
        var services = new ServiceCollection();

        // تسجيل وحدة العمل وسياق قاعدة البيانات
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddDbContext<InjazAccDbContext>();

        // تسجيل خدمات التطبيق
        services.AddApplicationServices();

        // تسجيل خدمة الحفظ التلقائي
        services.AddSingleton<IAutoSaveService, AutoSaveService>();

        // بناء حاوية الاعتمادية
        ServiceProvider = services.BuildServiceProvider();
    }

    /// <summary>
    /// الحصول على خدمة من حاوية الاعتمادية
    /// </summary>
    public static T GetService<T>()
    {
        return ServiceProvider.GetService<T>();
    }

    private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        try
        {
            Exception ex = (Exception)e.ExceptionObject;
            string errorMessage = $"حدث خطأ غير متوقع: {ex.Message}\n\nStack Trace:\n{ex.StackTrace}";

            // كتابة الخطأ في ملف سجل الأخطاء
            LogError(errorMessage);

            MessageBox.Show(errorMessage, "خطأ غير متوقع", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        catch
        {
            // في حالة حدوث خطأ أثناء معالجة الخطأ الأصلي
            MessageBox.Show("حدث خطأ غير متوقع في التطبيق.", "خطأ غير متوقع", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        try
        {
            string errorMessage = $"حدث خطأ غير متوقع: {e.Exception.Message}\n\nStack Trace:\n{e.Exception.StackTrace}";

            // كتابة الخطأ في ملف سجل الأخطاء
            LogError(errorMessage);

            MessageBox.Show(errorMessage, "خطأ غير متوقع", MessageBoxButton.OK, MessageBoxImage.Error);

            // تعليم الاستثناء كمعالج
            e.Handled = true;
        }
        catch
        {
            // في حالة حدوث خطأ أثناء معالجة الخطأ الأصلي
            MessageBox.Show("حدث خطأ غير متوقع في التطبيق.", "خطأ غير متوقع", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LogError(string errorMessage)
    {
        try
        {
            string logFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");

            // إنشاء مجلد السجلات إذا لم يكن موجوداً
            if (!Directory.Exists(logFolder))
            {
                Directory.CreateDirectory(logFolder);
            }

            string logFile = Path.Combine(logFolder, $"ErrorLog_{DateTime.Now:yyyy-MM-dd}.txt");
            string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {errorMessage}\n\n";

            // كتابة الخطأ في ملف السجل
            File.AppendAllText(logFile, logEntry);
        }
        catch
        {
            // تجاهل أي خطأ أثناء كتابة السجل
        }
    }

    /// <summary>
    /// معالج حدث إنهاء التطبيق - حفظ البيانات النهائي
    /// </summary>
    private void App_Exit(object sender, ExitEventArgs e)
    {
        try
        {
            // محاولة حفظ أي بيانات متبقية
            var unitOfWork = GetService<IUnitOfWork>();
            if (unitOfWork != null)
            {
                // استخدام الحفظ المتزامن لضمان الحفظ قبل الإغلاق
                var context = GetService<InjazAccDbContext>();
                if (context != null)
                {
                    context.SaveChanges();
                }

                // تنظيف الموارد
                unitOfWork.Dispose();
            }
        }
        catch (Exception ex)
        {
            // تسجيل الخطأ في ملف السجل
            LogError($"خطأ أثناء حفظ البيانات عند إنهاء التطبيق: {ex.Message}");
        }
    }
}

