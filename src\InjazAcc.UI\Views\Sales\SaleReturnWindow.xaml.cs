using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;

namespace InjazAcc.UI.Views.Sales
{
    /// <summary>
    /// Interaction logic for SaleReturnWindow.xaml
    /// </summary>
    public partial class SaleReturnWindow : Window
    {
        private bool _isReadOnly = false;
        private ReturnInvoice _returnInvoice;
        private Dictionary<string, InvoiceDetails> _invoicesData;

        public SaleReturnWindow()
        {
            InitializeComponent();
            dpReturnDate.SelectedDate = DateTime.Now;
            InitializeInvoicesData();
        }

        public SaleReturnWindow(ReturnInvoice returnInvoice, bool isReadOnly = false)
        {
            InitializeComponent();
            _returnInvoice = returnInvoice;
            _isReadOnly = isReadOnly;
            InitializeInvoicesData();

            // تعبئة بيانات المردود
            txtReturnTitle.Text = isReadOnly ? $"عرض مردود مبيعات رقم {returnInvoice.InvoiceNumber}" : $"تعديل مردود مبيعات رقم {returnInvoice.InvoiceNumber}";
            txtReturnNumber.Text = returnInvoice.InvoiceNumber;
            dpReturnDate.SelectedDate = returnInvoice.InvoiceDate;
            cmbInvoiceNumber.Text = returnInvoice.OriginalInvoiceNumber;
            txtCustomer.Text = returnInvoice.CustomerName;

            // تحميل أصناف المردود
            LoadSampleReturnItems();
            CalculateTotals();

            // تعطيل التحرير في حالة العرض فقط
            if (isReadOnly)
            {
                SetReadOnlyMode();
            }
        }

        private void InitializeInvoicesData()
        {
            // بيانات تجريبية للفواتير
            _invoicesData = new Dictionary<string, InvoiceDetails>
            {
                {
                    "INV-001", new InvoiceDetails
                    {
                        CustomerName = "شركة الأمل التجارية",
                        CustomerPhone = "0555123456",
                        InvoiceDate = DateTime.Now.AddDays(-1),
                        Items = new List<InvoiceItem>
                        {
                            new InvoiceItem { Code = "P001", Name = "لوح خشب متوسط", Unit = "قطعة", Quantity = 5, Price = 150.00m, DiscountPercentage = 0, TaxPercentage = 15 },
                            new InvoiceItem { Code = "P002", Name = "مسامير 5 سم", Unit = "كيلو", Quantity = 2, Price = 75.50m, DiscountPercentage = 0, TaxPercentage = 15 }
                        }
                    }
                },
                {
                    "INV-002", new InvoiceDetails
                    {
                        CustomerName = "مؤسسة النور",
                        CustomerPhone = "0555789012",
                        InvoiceDate = DateTime.Now.AddDays(-3),
                        Items = new List<InvoiceItem>
                        {
                            new InvoiceItem { Code = "P003", Name = "دهان أبيض", Unit = "جالون", Quantity = 3, Price = 120.25m, DiscountPercentage = 5, TaxPercentage = 15 },
                            new InvoiceItem { Code = "P004", Name = "زجاج شفاف", Unit = "متر", Quantity = 10, Price = 85.75m, DiscountPercentage = 0, TaxPercentage = 15 }
                        }
                    }
                },
                {
                    "INV-003", new InvoiceDetails
                    {
                        CustomerName = "شركة الإعمار",
                        CustomerPhone = "0555456789",
                        InvoiceDate = DateTime.Now.AddDays(-5),
                        Items = new List<InvoiceItem>
                        {
                            new InvoiceItem { Code = "P001", Name = "لوح خشب متوسط", Unit = "قطعة", Quantity = 20, Price = 150.00m, DiscountPercentage = 10, TaxPercentage = 15 },
                            new InvoiceItem { Code = "P005", Name = "أدوات سباكة", Unit = "علبة", Quantity = 5, Price = 220.00m, DiscountPercentage = 0, TaxPercentage = 15 }
                        }
                    }
                }
            };
        }

        private void SetReadOnlyMode()
        {
            dpReturnDate.IsEnabled = false;
            cmbInvoiceNumber.IsEnabled = false;
            cmbWarehouse.IsEnabled = false;
            btnAddItem.IsEnabled = false;
            dgItems.IsReadOnly = true;
            txtReason.IsReadOnly = true;
            btnSave.Visibility = Visibility.Collapsed;
            btnSaveAndPrint.Visibility = Visibility.Collapsed;
            btnCancel.Content = "إغلاق";
        }

        private void LoadSampleReturnItems()
        {
            // بيانات تجريبية لأصناف المردود
            var itemsList = new List<ReturnItem>
            {
                new ReturnItem { Code = "P001", Name = "لوح خشب متوسط", Unit = "قطعة", SoldQuantity = 5, ReturnQuantity = 2, Price = 150.00m, Total = 300.00m },
                new ReturnItem { Code = "P002", Name = "مسامير 5 سم", Unit = "كيلو", SoldQuantity = 2, ReturnQuantity = 1, Price = 75.50m, Total = 75.50m }
            };

            dgItems.ItemsSource = itemsList;
        }

        private void cmbInvoiceNumber_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbInvoiceNumber.SelectedItem != null)
            {
                string invoiceNumber = (cmbInvoiceNumber.SelectedItem as ComboBoxItem).Content.ToString();

                if (_invoicesData.ContainsKey(invoiceNumber))
                {
                    var invoiceDetails = _invoicesData[invoiceNumber];

                    // تعبئة بيانات العميل والفاتورة
                    txtCustomer.Text = invoiceDetails.CustomerName;
                    txtCustomerPhone.Text = invoiceDetails.CustomerPhone;
                    txtInvoiceDate.Text = invoiceDetails.InvoiceDate.ToString("yyyy-MM-dd");

                    // تحميل أصناف الفاتورة
                    var returnItems = new List<ReturnItem>();
                    foreach (var item in invoiceDetails.Items)
                    {
                        returnItems.Add(new ReturnItem
                        {
                            Code = item.Code,
                            Name = item.Name,
                            Unit = item.Unit,
                            SoldQuantity = item.Quantity,
                            ReturnQuantity = 0,
                            Price = item.Price,
                            Total = 0
                        });
                    }

                    dgItems.ItemsSource = returnItems;
                    CalculateTotals();
                }
            }
        }

        private void CalculateTotals()
        {
            decimal subTotal = 0;
            decimal totalTax = 0;
            decimal grandTotal = 0;

            if (dgItems.ItemsSource != null)
            {
                foreach (ReturnItem item in dgItems.ItemsSource)
                {
                    subTotal += item.Total;
                }

                // حساب الضريبة (15%)
                totalTax = subTotal * 0.15m;
                grandTotal = subTotal + totalTax;
            }

            txtSubTotal.Text = $"{subTotal:N2} ر.س";
            txtTotalTax.Text = $"{totalTax:N2} ر.س";
            txtGrandTotal.Text = $"{grandTotal:N2} ر.س";
        }

        private void btnAddItem_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من اختيار فاتورة
            if (string.IsNullOrEmpty(cmbInvoiceNumber.Text))
            {
                MessageBox.Show("الرجاء اختيار الفاتورة أولاً", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // فتح نافذة إضافة صنف للمردود
            var existingItems = dgItems.ItemsSource as List<ReturnItem> ?? new List<ReturnItem>();
            var returnItemWindow = new ReturnItemWindow(existingItems);
            if (returnItemWindow.ShowDialog() == true)
            {
                // تحديث قائمة الأصناف
                var updatedItems = new List<ReturnItem>();
                if (dgItems.ItemsSource != null)
                {
                    updatedItems.AddRange(dgItems.ItemsSource as List<ReturnItem>);
                }

                // تحديث الصنف المحدد أو إضافة صنف جديد
                int index = updatedItems.FindIndex(i => i.Code == returnItemWindow.Item.Code);
                if (index >= 0)
                {
                    updatedItems[index] = returnItemWindow.Item;
                }
                else
                {
                    updatedItems.Add(returnItemWindow.Item);
                }

                dgItems.ItemsSource = null;
                dgItems.ItemsSource = updatedItems;

                // إعادة حساب الإجماليات
                CalculateTotals();
            }
        }

        private void btnEditItem_Click(object sender, RoutedEventArgs e)
        {
            // تعديل الصنف المحدد
            var button = sender as Button;
            var item = button.DataContext as ReturnItem;

            // التحقق من أن العنصر ليس فارغاً
            if (item == null)
            {
                MessageBox.Show("خطأ: العنصر المحدد غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            var returnItemWindow = new ReturnItemWindow(item);
            if (returnItemWindow.ShowDialog() == true)
            {
                // تحديث قائمة الأصناف
                var updatedItems = new List<ReturnItem>();
                if (dgItems.ItemsSource != null)
                {
                    updatedItems.AddRange(dgItems.ItemsSource as List<ReturnItem>);
                }

                int index = updatedItems.FindIndex(i => i.Code == item.Code);
                if (index >= 0)
                {
                    updatedItems[index] = returnItemWindow.Item;
                }

                dgItems.ItemsSource = null;
                dgItems.ItemsSource = updatedItems;

                // إعادة حساب الإجماليات
                CalculateTotals();
            }
        }

        private void btnDeleteItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حذف الصنف المحدد
                var button = sender as Button;
                var item = button.DataContext as ReturnItem;

                // التحقق من أن العنصر ليس فارغاً
                if (item == null)
                {
                    MessageBox.Show("خطأ: العنصر المحدد غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var result = MessageBox.Show($"هل أنت متأكد من حذف الصنف {item.Name}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // حذف الصنف من المردود
                    var updatedItems = new List<ReturnItem>();
                    if (dgItems.ItemsSource != null)
                    {
                        updatedItems.AddRange(dgItems.ItemsSource as List<ReturnItem>);
                    }

                    updatedItems.RemoveAll(i => i.Code == item.Code);

                    dgItems.ItemsSource = null;
                    dgItems.ItemsSource = updatedItems;

                    // إعادة حساب الإجماليات
                    CalculateTotals();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف الصنف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            // حفظ المردود
            SaveReturn();
        }

        private void btnSaveAndPrint_Click(object sender, RoutedEventArgs e)
        {
            // حفظ وطباعة المردود
            if (SaveReturn())
            {
                MessageBox.Show("جاري طباعة المردود...", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private bool SaveReturn()
        {
            // التحقق من صحة البيانات
            if (dpReturnDate.SelectedDate == null)
            {
                MessageBox.Show("الرجاء تحديد تاريخ المردود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

            if (string.IsNullOrEmpty(cmbInvoiceNumber.Text))
            {
                MessageBox.Show("الرجاء اختيار الفاتورة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

            if (dgItems.Items.Count == 0)
            {
                MessageBox.Show("الرجاء إضافة صنف واحد على الأقل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

            bool hasReturnItems = false;
            foreach (ReturnItem item in dgItems.ItemsSource)
            {
                if (item.ReturnQuantity > 0)
                {
                    hasReturnItems = true;
                    break;
                }
            }

            if (!hasReturnItems)
            {
                MessageBox.Show("الرجاء تحديد كمية مردود لصنف واحد على الأقل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

            // حفظ المردود في قاعدة البيانات
            MessageBox.Show("تم حفظ المردود بنجاح", "تم", MessageBoxButton.OK, MessageBoxImage.Information);

            // إغلاق النافذة
            this.DialogResult = true;
            this.Close();

            return true;
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق النافذة
            this.DialogResult = false;
            this.Close();
        }
    }

    // فئات البيانات
    public class ReturnItem
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Unit { get; set; }
        public int SoldQuantity { get; set; }
        public int ReturnQuantity { get; set; }
        public decimal Price { get; set; }
        public decimal Total { get; set; }
    }

    public class InvoiceDetails
    {
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public DateTime InvoiceDate { get; set; }
        public List<InvoiceItem> Items { get; set; }
    }

    public class ReturnInvoice
    {
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public string CustomerName { get; set; }
        public string OriginalInvoiceNumber { get; set; }
        public decimal TotalAmount { get; set; }
        public string Status { get; set; }
    }
}
