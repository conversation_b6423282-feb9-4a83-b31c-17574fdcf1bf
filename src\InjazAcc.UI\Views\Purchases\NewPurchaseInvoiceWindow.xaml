<Window x:Class="InjazAcc.UI.Views.Purchases.NewPurchaseInvoiceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Purchases"
        mc:Ignorable="d"
        Title="فاتورة مشتريات جديدة" Height="650" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        TextElement.FontSize="14"
        TextElement.FontWeight="Regular"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان الفاتورة -->
        <TextBlock Grid.Row="0" Text="فاتورة مشتريات جديدة" Style="{StaticResource PageTitle}" HorizontalAlignment="Center"/>

        <!-- معلومات الفاتورة -->
        <Grid Grid.Row="1" Margin="0,10,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- معلومات الفاتورة - الجانب الأيمن -->
            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                <TextBlock Text="معلومات الفاتورة" FontWeight="Bold" Margin="0,0,0,10"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم الفاتورة:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="0" Grid.Column="1" Text="PUR-00001" IsReadOnly="True" Margin="0,5"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="التاريخ:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <DatePicker Grid.Row="1" Grid.Column="1" SelectedDate="{Binding Today}" Margin="0,5"/>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="الحالة:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <ComboBox Grid.Row="2" Grid.Column="1" SelectedIndex="0" Margin="0,5">
                        <ComboBoxItem Content="مؤكدة"/>
                        <ComboBoxItem Content="مدفوعة"/>
                        <ComboBoxItem Content="مدفوعة جزئياً"/>
                        <ComboBoxItem Content="ملغاة"/>
                    </ComboBox>
                </Grid>
            </StackPanel>

            <!-- معلومات المورد - الجانب الأيسر -->
            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                <TextBlock Text="معلومات المورد" FontWeight="Bold" Margin="0,0,0,10"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="المورد:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <Grid Grid.Row="0" Grid.Column="1" Margin="0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <ComboBox Grid.Column="0" x:Name="cmbSuppliers">
                            <ComboBoxItem Content="شركة التوريدات العامة"/>
                            <ComboBoxItem Content="مؤسسة الإمداد"/>
                            <ComboBoxItem Content="شركة المواد الأولية"/>
                            <ComboBoxItem Content="مؤسسة التجهيزات"/>
                            <ComboBoxItem Content="شركة المعدات الحديثة"/>
                        </ComboBox>
                        <Button Grid.Column="1" Style="{StaticResource MaterialDesignFlatButton}"
                                ToolTip="إضافة مورد جديد" Margin="5,0,0,0" Padding="5"
                                Click="btnAddNewSupplier_Click">
                            <materialDesign:PackIcon Kind="AccountPlus" Width="20" Height="20"/>
                        </Button>
                    </Grid>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="الهاتف:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtPhone" Text="**********" Margin="0,5"/>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="العنوان:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtAddress" Text="الرياض - شارع الملك فهد" Margin="0,5"/>
                </Grid>
            </StackPanel>
        </Grid>

        <!-- أزرار إضافة المنتجات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10">
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Margin="0,0,10,0" Click="btnAddItem_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Plus" VerticalAlignment="Center"/>
                    <TextBlock Text="إضافة منتج" Margin="5,0,0,0"/>
                </StackPanel>
            </Button>
        </StackPanel>

        <!-- جدول المنتجات -->
        <DataGrid Grid.Row="3" x:Name="dgItems" AutoGenerateColumns="False" CanUserAddRows="False" Margin="0,5" Style="{StaticResource DataGridStyle}">
            <DataGrid.Columns>
                <DataGridTextColumn Header="الرمز" Binding="{Binding Code}" Width="100"/>
                <DataGridTextColumn Header="اسم المنتج" Binding="{Binding Name}" Width="*"/>
                <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80"/>
                <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity, StringFormat=N2}" Width="100"/>
                <DataGridTextColumn Header="السعر" Binding="{Binding Price, StringFormat=N2}" Width="120"/>
                <DataGridTextColumn Header="الخصم" Binding="{Binding Discount, StringFormat=N2}" Width="100"/>
                <DataGridTextColumn Header="الإجمالي" Binding="{Binding Total, StringFormat=N2}" Width="120"/>
                <DataGridTemplateColumn Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="تعديل" Click="btnEditItem_Click">
                                    <materialDesign:PackIcon Kind="Pencil" Width="18" Height="18"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="حذف" Click="btnDeleteItem_Click">
                                    <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                </Button>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- ملخص الفاتورة -->
        <Grid Grid.Row="4" Margin="0,10,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- ملاحظات -->
            <StackPanel Grid.Column="0" Margin="0,0,20,0">
                <TextBlock Text="ملاحظات" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="txtNotes" TextWrapping="Wrap" AcceptsReturn="True" Height="100"/>
            </StackPanel>

            <!-- المجاميع -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="إجمالي المنتجات:" HorizontalAlignment="Right" Margin="0,5"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="txtSubtotal" Text="1,250.00 ر.س" Width="120" HorizontalAlignment="Left" Margin="10,5"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="الخصم:" HorizontalAlignment="Right" Margin="0,5"/>
                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="txtTotalDiscount" Text="50.00 ر.س" Width="120" HorizontalAlignment="Left" Margin="10,5"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="الضريبة (15%):" HorizontalAlignment="Right" Margin="0,5"/>
                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="txtTax" Text="180.00 ر.س" Width="120" HorizontalAlignment="Left" Margin="10,5"/>

                <Separator Grid.Row="3" Grid.ColumnSpan="2" Margin="0,5"/>

                <TextBlock Grid.Row="4" Grid.Column="0" Text="الإجمالي النهائي:" HorizontalAlignment="Right" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="4" Grid.Column="1" x:Name="txtTotal" Text="1,380.00 ر.س" Width="120" HorizontalAlignment="Left" FontWeight="Bold" Margin="10,5"/>

                <!-- طريقة الدفع -->
                <TextBlock Grid.Row="5" Grid.Column="0" Text="طريقة الدفع:" HorizontalAlignment="Right" Margin="0,10,0,5"/>
                <StackPanel Grid.Row="5" Grid.Column="1" Orientation="Horizontal" Margin="10,10,0,5">
                    <RadioButton x:Name="rbCash" Content="نقدًا" Margin="0,0,10,0" IsChecked="True" Checked="PaymentMethod_Checked"/>
                    <RadioButton x:Name="rbPartial" Content="جزئي" Margin="0,0,10,0" Checked="PaymentMethod_Checked"/>
                    <RadioButton x:Name="rbCredit" Content="آجل" Margin="0,0,10,0" Checked="PaymentMethod_Checked"/>
                </StackPanel>

                <!-- المبلغ المسدد -->
                <TextBlock Grid.Row="6" Grid.Column="0" Text="المبلغ المسدد:" HorizontalAlignment="Right" Margin="0,5"/>
                <TextBox Grid.Row="6" Grid.Column="1" x:Name="txtPaidAmount" Width="120" HorizontalAlignment="Left"
                         Margin="10,5" IsEnabled="False" TextChanged="txtPaidAmount_TextChanged"/>
            </Grid>
        </Grid>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Style="{StaticResource ActionButton}" Content="حفظ الفاتورة" Margin="5,0" Click="btnSave_Click"/>
            <Button Style="{StaticResource ActionButton}" Content="حفظ وطباعة" Margin="5,0" Click="btnSaveAndPrint_Click"/>
            <Button Style="{StaticResource ActionButton}" Content="معاينة الطباعة" Margin="5,0" Click="btnPrintPreview_Click"/>
            <Button Style="{StaticResource ActionButton}" Content="إلغاء" Margin="5,0" Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
