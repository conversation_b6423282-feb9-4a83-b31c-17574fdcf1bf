using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace InjazAcc.UI.Views.Customers
{
    /// <summary>
    /// Interaction logic for CustomersPage.xaml
    /// </summary>
    public partial class CustomersPage : Page
    {
        private ObservableCollection<Customer> _customers;

        public CustomersPage()
        {
            InitializeComponent();
            LoadSampleData();
            UpdateStatistics();
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية للعملاء
                _customers = new ObservableCollection<Customer>
                {
                    new Customer { Code = "C001", Name = "شركة الأمل للتجارة", Phone = "0555123456", Email = "<EMAIL>", Address = "الرياض - شارع الملك فهد", Balance = 15000 },
                    new Customer { Code = "C002", Name = "مؤسسة النور", Phone = "0555789012", Email = "<EMAIL>", Address = "جدة - شارع التحلية", Balance = 8500 },
                    new Customer { Code = "C003", Name = "شركة الصفا للمقاولات", Phone = "0555456789", Email = "<EMAIL>", Address = "الدمام - شارع الأمير محمد", Balance = 25000 },
                    new Customer { Code = "C004", Name = "مؤسسة الإبداع", Phone = "0555234567", Email = "<EMAIL>", Address = "الرياض - شارع العليا", Balance = 12000 },
                    new Customer { Code = "C005", Name = "شركة المستقبل", Phone = "0555345678", Email = "<EMAIL>", Address = "مكة - شارع العزيزية", Balance = 18000 }
                };
                
                dgCustomers.ItemsSource = _customers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            try
            {
                // تحديث الإحصائيات
                txtTotalCustomers.Text = _customers.Count.ToString();
                
                double totalSales = 0;
                double totalDue = 0;
                
                foreach (var customer in _customers)
                {
                    totalSales += customer.TotalSales;
                    totalDue += customer.Balance;
                }
                
                txtTotalSales.Text = totalSales.ToString("N2") + " ر.س";
                txtTotalDue.Text = totalDue.ToString("N2") + " ر.س";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث الإحصائيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string searchText = txtSearch.Text.Trim();
                if (string.IsNullOrEmpty(searchText))
                {
                    dgCustomers.ItemsSource = _customers;
                    return;
                }

                string searchBy = (cmbSearchBy.SelectedItem as ComboBoxItem)?.Content.ToString();
                var filteredCustomers = new ObservableCollection<Customer>();

                foreach (var customer in _customers)
                {
                    bool isMatch = false;

                    switch (searchBy)
                    {
                        case "الاسم":
                            isMatch = customer.Name.Contains(searchText);
                            break;
                        case "رقم الهاتف":
                            isMatch = customer.Phone.Contains(searchText);
                            break;
                        case "البريد الإلكتروني":
                            isMatch = customer.Email.Contains(searchText);
                            break;
                        case "العنوان":
                            isMatch = customer.Address.Contains(searchText);
                            break;
                        default:
                            isMatch = customer.Name.Contains(searchText) || customer.Phone.Contains(searchText);
                            break;
                    }

                    if (isMatch)
                    {
                        filteredCustomers.Add(customer);
                    }
                }

                dgCustomers.ItemsSource = filteredCustomers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnAddCustomer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var customerWindow = new CustomerWindow();
                if (customerWindow.ShowDialog() == true)
                {
                    _customers.Add(customerWindow.Customer);
                    UpdateStatistics();
                    MessageBox.Show("تم إضافة العميل بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEditCustomer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var customer = button.DataContext as Customer;
                
                if (customer != null)
                {
                    var customerWindow = new CustomerWindow(customer);
                    if (customerWindow.ShowDialog() == true)
                    {
                        int index = _customers.IndexOf(customer);
                        if (index >= 0)
                        {
                            _customers[index] = customerWindow.Customer;
                            dgCustomers.Items.Refresh();
                            UpdateStatistics();
                            MessageBox.Show("تم تعديل العميل بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnDeleteCustomer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var customer = button.DataContext as Customer;
                
                if (customer != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف العميل: {customer.Name}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    
                    if (result == MessageBoxResult.Yes)
                    {
                        _customers.Remove(customer);
                        UpdateStatistics();
                        MessageBox.Show("تم حذف العميل بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCustomerDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var customer = button.DataContext as Customer;
                
                if (customer != null)
                {
                    var customerDetailsWindow = new CustomerDetailsWindow(customer);
                    customerDetailsWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void dgCustomers_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (dgCustomers.SelectedItem is Customer customer)
                {
                    var customerDetailsWindow = new CustomerDetailsWindow(customer);
                    customerDetailsWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class Customer
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public double Balance { get; set; }
        public double TotalSales { get; set; } = 0; // قيمة افتراضية للمبيعات
    }
}
