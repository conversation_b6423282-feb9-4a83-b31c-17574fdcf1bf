<Window x:Class="InjazAcc.UI.Views.Accounts.ExpenseWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Accounts"
        mc:Ignorable="d"
        Title="إضافة مصروف جديد" Height="400" Width="500"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        TextElement.FontSize="14"
        TextElement.FontWeight="Regular"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- عنوان النافذة -->
        <TextBlock Grid.Row="0" Text="إضافة مصروف جديد" Style="{StaticResource PageTitle}" HorizontalAlignment="Center" Margin="0,0,0,20"/>
        
        <!-- نموذج إدخال البيانات -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- التاريخ -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="التاريخ:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <DatePicker Grid.Row="0" Grid.Column="1" x:Name="dpDate" Margin="0,5"/>
            
            <!-- المبلغ -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="المبلغ:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtAmount" Margin="0,5"/>
            
            <!-- الحساب -->
            <TextBlock Grid.Row="2" Grid.Column="0" Text="الحساب:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <ComboBox Grid.Row="2" Grid.Column="1" x:Name="cmbAccount" Margin="0,5">
                <ComboBoxItem Content="مصروف الرواتب"/>
                <ComboBoxItem Content="مصروف الإيجار"/>
                <ComboBoxItem Content="مصروفات المرافق"/>
                <ComboBoxItem Content="مصروفات عمومية وإدارية"/>
                <ComboBoxItem Content="مصروفات أخرى"/>
            </ComboBox>
            
            <!-- البيان -->
            <TextBlock Grid.Row="3" Grid.Column="0" Text="البيان:" Margin="0,0,10,0" VerticalAlignment="Top"/>
            <TextBox Grid.Row="3" Grid.Column="1" x:Name="txtDescription" TextWrapping="Wrap" AcceptsReturn="True" Height="80" Margin="0,5"/>
            
            <!-- المرفقات -->
            <TextBlock Grid.Row="4" Grid.Column="0" Text="المرفقات:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <Grid Grid.Row="4" Grid.Column="1" Margin="0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBox Grid.Column="0" x:Name="txtAttachment" IsReadOnly="True" Margin="0,0,10,0"/>
                <Button Grid.Column="1" Style="{StaticResource MaterialDesignRaisedButton}" Content="استعراض" Click="btnBrowse_Click"/>
            </Grid>
        </Grid>
        
        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="حفظ" Margin="5,0" Click="btnSave_Click"/>
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="إلغاء" Margin="5,0" Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
