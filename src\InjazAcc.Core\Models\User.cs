using System;
using System.Collections.Generic;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج المستخدم في النظام
    /// </summary>
    public class User
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string PasswordHash { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLogin { get; set; }

        // العلاقة مع الأدوار
        public virtual ICollection<UserRole> UserRoles { get; set; }

        // العلاقة المباشرة مع الدور (للتبسيط في واجهة المستخدم)
        public int RoleId { get; set; }
    }
}
