using System;

namespace InjazAcc.Core.Models.FinancialStatements
{
    /// <summary>
    /// نموذج بيانات عنصر في ميزان المراجعة
    /// </summary>
    public class TrialBalanceEntry
    {
        /// <summary>
        /// رقم الحساب
        /// </summary>
        public string AccountCode { get; set; }
        
        /// <summary>
        /// اسم الحساب
        /// </summary>
        public string AccountName { get; set; }
        
        /// <summary>
        /// نوع الحساب
        /// </summary>
        public AccountType AccountType { get; set; }
        
        /// <summary>
        /// الرصيد المدين
        /// </summary>
        public decimal DebitBalance { get; set; }
        
        /// <summary>
        /// الرصيد الدائن
        /// </summary>
        public decimal CreditBalance { get; set; }
        
        /// <summary>
        /// تاريخ الرصيد
        /// </summary>
        public DateTime BalanceDate { get; set; }
    }
}
