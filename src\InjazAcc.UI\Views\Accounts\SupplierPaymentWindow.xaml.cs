using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;

namespace InjazAcc.UI.Views.Accounts
{
    /// <summary>
    /// Interaction logic for SupplierPaymentWindow.xaml
    /// </summary>
    public partial class SupplierPaymentWindow : Window
    {
        private Dictionary<string, double> _supplierBalances;
        
        public DateTime TransactionDate { get; private set; }
        public string SupplierName { get; private set; }
        public double Amount { get; private set; }
        public string PaymentMethod { get; private set; }
        public string Description { get; private set; }
        public string Attachment { get; private set; }

        public SupplierPaymentWindow()
        {
            InitializeComponent();
            dpDate.SelectedDate = DateTime.Now;
            cmbSupplier.SelectedIndex = 0;
            cmbPaymentMethod.SelectedIndex = 0;
            
            // بيانات تجريبية لأرصدة الموردين
            _supplierBalances = new Dictionary<string, double>
            {
                { "شركة التوريدات العامة", 25000 },
                { "مؤسسة الإمداد", 18000 },
                { "شركة المواد الأولية", 32000 },
                { "مؤسسة التجهيزات", 15000 },
                { "شركة المعدات المكتبية", 22000 }
            };
            
            UpdateSupplierBalance();
        }

        private void UpdateSupplierBalance()
        {
            try
            {
                string supplierName = (cmbSupplier.SelectedItem as ComboBoxItem)?.Content.ToString();
                if (!string.IsNullOrEmpty(supplierName) && _supplierBalances.ContainsKey(supplierName))
                {
                    txtSupplierBalance.Text = _supplierBalances[supplierName].ToString("N2") + " ر.س";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void cmbSupplier_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateSupplierBalance();
        }

        private void btnBrowse_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|ملفات الصور (*.jpg, *.jpeg, *.png)|*.jpg;*.jpeg;*.png";
                
                if (openFileDialog.ShowDialog() == true)
                {
                    txtAttachment.Text = openFileDialog.FileName;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!dpDate.SelectedDate.HasValue)
                {
                    MessageBox.Show("الرجاء تحديد التاريخ", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    dpDate.Focus();
                    return;
                }

                if (cmbSupplier.SelectedItem == null)
                {
                    MessageBox.Show("الرجاء اختيار المورد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    cmbSupplier.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtAmount.Text))
                {
                    MessageBox.Show("الرجاء إدخال المبلغ", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtAmount.Focus();
                    return;
                }

                if (!double.TryParse(txtAmount.Text, out double amount) || amount <= 0)
                {
                    MessageBox.Show("الرجاء إدخال مبلغ صحيح أكبر من صفر", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtAmount.Focus();
                    return;
                }

                string supplierName = (cmbSupplier.SelectedItem as ComboBoxItem)?.Content.ToString();
                if (_supplierBalances.ContainsKey(supplierName) && amount > _supplierBalances[supplierName])
                {
                    MessageBox.Show("المبلغ المدخل أكبر من رصيد المورد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtAmount.Focus();
                    return;
                }

                if (cmbPaymentMethod.SelectedItem == null)
                {
                    MessageBox.Show("الرجاء اختيار طريقة الدفع", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    cmbPaymentMethod.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtDescription.Text))
                {
                    MessageBox.Show("الرجاء إدخال البيان", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtDescription.Focus();
                    return;
                }

                // حفظ البيانات
                TransactionDate = dpDate.SelectedDate.Value;
                SupplierName = supplierName;
                Amount = amount;
                PaymentMethod = (cmbPaymentMethod.SelectedItem as ComboBoxItem)?.Content.ToString();
                Description = txtDescription.Text;
                Attachment = txtAttachment.Text;

                // تحديث رصيد المورد
                if (_supplierBalances.ContainsKey(supplierName))
                {
                    _supplierBalances[supplierName] -= amount;
                }

                // إغلاق النافذة بنجاح
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إلغاء العملية وإغلاق النافذة
            this.DialogResult = false;
        }
    }
}
