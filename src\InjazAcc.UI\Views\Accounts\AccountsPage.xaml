<Page x:Class="InjazAcc.UI.Views.Accounts.AccountsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Accounts"
      mc:Ignorable="d" 
      d:DesignHeight="650" d:DesignWidth="900"
      Title="الحسابات"
      FlowDirection="RightToLeft"
      FontFamily="Arial"
      TextElement.FontSize="14"
      TextElement.FontWeight="Regular"
      TextElement.Foreground="{DynamicResource MaterialDesignBody}"
      Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- عنوان الصفحة -->
        <TextBlock Grid.Row="0" Text="إدارة الحسابات" Style="{StaticResource PageTitle}" Margin="20,20,20,10"/>
        
        <!-- بطاقات الوظائف -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- بطاقة أرصدة أول المدة -->
            <Border Grid.Row="0" Grid.Column="0" Margin="10" Background="#4CAF50" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="OpeningBalances_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="أرصدة أول المدة" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="CashRegister" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="إدارة أرصدة أول المدة لجميع الحسابات" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- بطاقة الخزينة/النقدية -->
            <Border Grid.Row="0" Grid.Column="1" Margin="10" Background="#2196F3" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="Cash_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="الخزينة / النقدية" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="Cash" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="إدارة المصروفات والإيرادات والمدفوعات والتحصيلات" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- بطاقة الحسابات الختامية -->
            <Border Grid.Row="1" Grid.Column="0" Margin="10" Background="#FF9800" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="FinancialStatements_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="الحسابات الختامية" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="ChartLine" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="ميزان المراجعة وقائمة الدخل والميزانية العمومية" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- بطاقة حساب الأستاذ -->
            <Border Grid.Row="1" Grid.Column="1" Margin="10" Background="#9C27B0" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="Ledger_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="حساب الأستاذ" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="BookOpenPageVariant" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="عرض تفاصيل حركات أي حساب" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- بطاقة دليل الحسابات -->
            <Border Grid.Row="2" Grid.Column="0" Margin="10" Background="#607D8B" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="ChartOfAccounts_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="دليل الحسابات" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="FileTree" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="إدارة دليل الحسابات وإضافة حسابات جديدة" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- بطاقة التقارير المالية -->
            <Border Grid.Row="2" Grid.Column="1" Margin="10" Background="#E91E63" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="FinancialReports_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="التقارير المالية" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="FileChart" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="تقارير مالية متنوعة وتحليلات" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Page>
