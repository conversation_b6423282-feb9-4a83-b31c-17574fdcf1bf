<Window x:Class="InjazAcc.UI.Views.Suppliers.SupplierDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Suppliers"
        mc:Ignorable="d"
        Title="تفاصيل المورد" Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        TextElement.FontSize="14"
        TextElement.FontWeight="Regular"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان النافذة -->
        <TextBlock Grid.Row="0" x:Name="txtSupplierName" Text="تفاصيل المورد: شركة التوريدات العامة" Style="{StaticResource PageTitle}" HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- معلومات المورد -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- معلومات المورد - الجانب الأيمن -->
            <Grid Grid.Column="0" Margin="0,0,10,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="رمز المورد:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="txtCode" Text="S001" Margin="10,5"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="اسم المورد:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="txtName" Text="شركة التوريدات العامة" Margin="10,5"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="رقم الهاتف:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="txtPhone" Text="0555111222" Margin="10,5"/>

                <TextBlock Grid.Row="3" Grid.Column="0" Text="البريد الإلكتروني:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="3" Grid.Column="1" x:Name="txtEmail" Text="<EMAIL>" Margin="10,5"/>
            </Grid>

            <!-- معلومات المورد - الجانب الأيسر -->
            <Grid Grid.Column="1" Margin="10,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="العنوان:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="txtAddress" Text="الرياض - شارع العليا" Margin="10,5"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="الرصيد:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="txtBalance" Text="25,000.00 ر.س" Margin="10,5"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="إجمالي المشتريات:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="txtTotalPurchases" Text="150,000.00 ر.س" Margin="10,5"/>

                <TextBlock Grid.Row="3" Grid.Column="0" Text="آخر معاملة:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="3" Grid.Column="1" x:Name="txtLastTransaction" Text="2023-05-10" Margin="10,5"/>
            </Grid>
        </Grid>

        <!-- تبويبات التفاصيل -->
        <TabControl Grid.Row="2" Style="{StaticResource MaterialDesignTabControl}">
            <!-- تبويب الفواتير -->
            <TabItem Header="الفواتير">
                <DataGrid x:Name="dgInvoices" AutoGenerateColumns="False" CanUserAddRows="False" Style="{StaticResource DataGridStyle}">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=yyyy-MM-dd}" Width="120"/>
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="120"/>
                        <DataGridTextColumn Header="المدفوع" Binding="{Binding Paid, StringFormat=N2}" Width="120"/>
                        <DataGridTextColumn Header="المتبقي" Binding="{Binding Remaining, StringFormat=N2}" Width="120"/>
                        <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                        <DataGridTemplateColumn Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="عرض الفاتورة" Click="btnViewInvoice_Click">
                                        <materialDesign:PackIcon Kind="FileDocument" Width="18" Height="18"/>
                                    </Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>

            <!-- تبويب المدفوعات -->
            <TabItem Header="المدفوعات">
                <DataGrid x:Name="dgPayments" AutoGenerateColumns="False" CanUserAddRows="False" Style="{StaticResource DataGridStyle}">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم السند" Binding="{Binding ReceiptNumber}" Width="120"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=yyyy-MM-dd}" Width="120"/>
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="120"/>
                        <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethod}" Width="120"/>
                        <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="*"/>
                        <DataGridTemplateColumn Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="عرض السند" Click="btnViewReceipt_Click">
                                        <materialDesign:PackIcon Kind="Receipt" Width="18" Height="18"/>
                                    </Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>

            <!-- تبويب المرتجعات -->
            <TabItem Header="المرتجعات">
                <DataGrid x:Name="dgReturns" AutoGenerateColumns="False" CanUserAddRows="False" Style="{StaticResource DataGridStyle}">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم المرتجع" Binding="{Binding ReturnNumber}" Width="120"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=yyyy-MM-dd}" Width="120"/>
                        <DataGridTextColumn Header="رقم الفاتورة الأصلية" Binding="{Binding OriginalInvoiceNumber}" Width="150"/>
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="120"/>
                        <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="*"/>
                        <DataGridTemplateColumn Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="عرض المرتجع" Click="btnViewReturn_Click">
                                        <materialDesign:PackIcon Kind="KeyboardReturn" Width="18" Height="18"/>
                                    </Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>
        </TabControl>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="تعديل بيانات المورد" Margin="5,0" Click="btnEditSupplier_Click"/>
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="طباعة كشف حساب" Margin="5,0" Click="btnPrintStatement_Click"/>
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="إغلاق" Margin="5,0" Click="btnClose_Click"/>
        </StackPanel>
    </Grid>
</Window>
