using System.Collections.Generic;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج فئة المنتجات في النظام
    /// </summary>
    public class Category
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        
        // العلاقة مع الفئة الأب (للفئات الفرعية)
        public int? ParentCategoryId { get; set; }
        public virtual Category ParentCategory { get; set; }
        
        // العلاقة مع الفئات الفرعية
        public virtual ICollection<Category> SubCategories { get; set; }
        
        // العلاقة مع المنتجات
        public virtual ICollection<Product> Products { get; set; }
    }
}
