        /// <summary>
        /// إنشاء قيد محاسبي لمصروف
        /// </summary>
        public async Task<JournalEntry> CreateExpenseEntryAsync(Payment payment, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (payment == null)
                    throw new ArgumentNullException(nameof(payment));
                
                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"EXP-{payment.ReferenceNumber}",
                    EntryDate = payment.PaymentDate,
                    Type = JournalEntryType.Payment,
                    Description = $"قيد مصروف رقم {payment.ReferenceNumber}",
                    UserId = userId,
                    PaymentId = payment.Id,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };
                
                // الحصول على معرفات الحسابات
                int treasuryAccountId = await GetTreasuryAccountIdAsync();
                
                // إضافة عناصر القيد
                
                // 1. مدين: حساب المصروف (بقيمة المصروف)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = payment.AccountId,
                    Description = $"مصروف رقم {payment.ReferenceNumber}",
                    DebitAmount = payment.Amount,
                    CreditAmount = 0
                });
                
                // 2. دائن: حساب الخزينة (بقيمة المصروف)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = treasuryAccountId,
                    Description = $"دفع مصروف رقم {payment.ReferenceNumber}",
                    DebitAmount = 0,
                    CreditAmount = payment.Amount
                });
                
                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);
                
                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();
                
                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد مصروف: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// إنشاء قيد محاسبي لإيراد
        /// </summary>
        public async Task<JournalEntry> CreateRevenueEntryAsync(Payment payment, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (payment == null)
                    throw new ArgumentNullException(nameof(payment));
                
                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"REV-{payment.ReferenceNumber}",
                    EntryDate = payment.PaymentDate,
                    Type = JournalEntryType.Payment,
                    Description = $"قيد إيراد رقم {payment.ReferenceNumber}",
                    UserId = userId,
                    PaymentId = payment.Id,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };
                
                // الحصول على معرفات الحسابات
                int treasuryAccountId = await GetTreasuryAccountIdAsync();
                
                // إضافة عناصر القيد
                
                // 1. مدين: حساب الخزينة (بقيمة الإيراد)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = treasuryAccountId,
                    Description = $"استلام إيراد رقم {payment.ReferenceNumber}",
                    DebitAmount = payment.Amount,
                    CreditAmount = 0
                });
                
                // 2. دائن: حساب الإيراد (بقيمة الإيراد)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = payment.AccountId,
                    Description = $"إيراد رقم {payment.ReferenceNumber}",
                    DebitAmount = 0,
                    CreditAmount = payment.Amount
                });
                
                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);
                
                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();
                
                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد إيراد: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// إنشاء قيد محاسبي لتسوية مخزون
        /// </summary>
        public async Task<JournalEntry> CreateInventoryAdjustmentEntryAsync(InventoryTransfer adjustment, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (adjustment == null)
                    throw new ArgumentNullException(nameof(adjustment));
                
                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"ADJ-{adjustment.ReferenceNumber}",
                    EntryDate = adjustment.TransferDate,
                    Type = JournalEntryType.InventoryAdjustment,
                    Description = $"قيد تسوية مخزون رقم {adjustment.ReferenceNumber}",
                    UserId = userId,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };
                
                // الحصول على معرفات الحسابات
                int inventoryAccountId = await GetInventoryAccountIdAsync();
                
                // حساب قيمة التسوية
                decimal adjustmentValue = CalculateAdjustmentValue(adjustment);
                
                // إضافة عناصر القيد حسب نوع التسوية (زيادة أو نقصان)
                if (adjustmentValue > 0)
                {
                    // زيادة في المخزون
                    
                    // 1. مدين: حساب المخزون (بقيمة الزيادة)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = inventoryAccountId,
                        Description = $"زيادة في المخزون من تسوية رقم {adjustment.ReferenceNumber}",
                        DebitAmount = adjustmentValue,
                        CreditAmount = 0
                    });
                    
                    // 2. دائن: حساب تسويات المخزون (بقيمة الزيادة)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = await GetAccountIdByTypeAsync(AccountType.Revenue),
                        Description = $"إيراد من تسوية مخزون رقم {adjustment.ReferenceNumber}",
                        DebitAmount = 0,
                        CreditAmount = adjustmentValue
                    });
                }
                else if (adjustmentValue < 0)
                {
                    // نقصان في المخزون
                    
                    // 1. مدين: حساب تسويات المخزون (بقيمة النقصان)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = await GetAccountIdByTypeAsync(AccountType.Expense),
                        Description = $"مصروف من تسوية مخزون رقم {adjustment.ReferenceNumber}",
                        DebitAmount = Math.Abs(adjustmentValue),
                        CreditAmount = 0
                    });
                    
                    // 2. دائن: حساب المخزون (بقيمة النقصان)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = inventoryAccountId,
                        Description = $"نقصان في المخزون من تسوية رقم {adjustment.ReferenceNumber}",
                        DebitAmount = 0,
                        CreditAmount = Math.Abs(adjustmentValue)
                    });
                }
                
                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);
                
                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();
                
                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد تسوية مخزون: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// حساب قيمة تسوية المخزون
        /// </summary>
        private decimal CalculateAdjustmentValue(InventoryTransfer adjustment)
        {
            // في التطبيق الحقيقي، سيتم حساب قيمة التسوية بناءً على قيمة المنتجات
            // هنا نفترض قيمة افتراضية للتجربة
            return 1000;
        }
