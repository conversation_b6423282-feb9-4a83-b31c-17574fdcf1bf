using System;
using System.Threading.Tasks;
using InjazAcc.Core.Models.FinancialStatements;

namespace InjazAcc.Core.Interfaces
{
    /// <summary>
    /// واجهة خدمة الحسابات الختامية
    /// </summary>
    public interface IFinancialStatementsService
    {
        /// <summary>
        /// الحصول على ميزان المراجعة
        /// </summary>
        /// <param name="fromDate">تاريخ البداية</param>
        /// <param name="toDate">تاريخ النهاية</param>
        /// <returns>نتائج ميزان المراجعة</returns>
        Task<FinancialStatementResult> GetTrialBalanceAsync(DateTime fromDate, DateTime toDate);
        
        /// <summary>
        /// الحصول على قائمة الدخل
        /// </summary>
        /// <param name="fromDate">تاريخ البداية</param>
        /// <param name="toDate">تاريخ النهاية</param>
        /// <returns>نتائج قائمة الدخل</returns>
        Task<FinancialStatementResult> GetIncomeStatementAsync(DateTime fromDate, DateTime toDate);
        
        /// <summary>
        /// الحصول على الميزانية العمومية
        /// </summary>
        /// <param name="asOfDate">تاريخ الميزانية</param>
        /// <returns>نتائج الميزانية العمومية</returns>
        Task<FinancialStatementResult> GetBalanceSheetAsync(DateTime asOfDate);
        
        /// <summary>
        /// الحصول على جميع الحسابات الختامية
        /// </summary>
        /// <param name="fromDate">تاريخ البداية</param>
        /// <param name="toDate">تاريخ النهاية</param>
        /// <returns>نتائج الحسابات الختامية</returns>
        Task<FinancialStatementResult> GetAllFinancialStatementsAsync(DateTime fromDate, DateTime toDate);
        
        /// <summary>
        /// إقفال الحسابات في نهاية السنة المالية
        /// </summary>
        /// <param name="fiscalYearId">معرف السنة المالية</param>
        /// <param name="closingDate">تاريخ الإقفال</param>
        /// <param name="userId">معرف المستخدم الذي يقوم بالإقفال</param>
        /// <returns>نجاح أو فشل العملية</returns>
        Task<bool> CloseAccountsForFiscalYearAsync(int fiscalYearId, DateTime closingDate, int userId);
    }
}
