using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using InjazAcc.Core.Interfaces;
using InjazAcc.Core.Models;

namespace InjazAcc.Services
{
    /// <summary>
    /// خدمة القيود المحاسبية
    /// </summary>
    public class AccountingService : IAccountingService
    {
        private readonly IUnitOfWork _unitOfWork;
        
        public AccountingService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        
        /// <summary>
        /// إنشاء قيد محاسبي لفاتورة مبيعات
        /// </summary>
        public async Task<JournalEntry> CreateSalesInvoiceEntryAsync(Invoice invoice, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (invoice == null)
                    throw new ArgumentNullException(nameof(invoice));
                
                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"INV-{invoice.InvoiceNumber}",
                    EntryDate = invoice.InvoiceDate,
                    Type = JournalEntryType.Invoice,
                    Description = $"قيد فاتورة مبيعات رقم {invoice.InvoiceNumber}",
                    UserId = userId,
                    InvoiceId = invoice.Id,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };
                
                // الحصول على معرفات الحسابات
                int salesAccountId = await GetSalesAccountIdAsync();
                int customersAccountId = await GetCustomersAccountIdAsync();
                int treasuryAccountId = await GetTreasuryAccountIdAsync();
                int inventoryAccountId = await GetInventoryAccountIdAsync();
                int costOfSalesAccountId = await GetCostOfSalesAccountIdAsync();
                
                // إضافة عناصر القيد
                
                // 1. مدين: حساب العملاء أو الخزينة (حسب طريقة الدفع)
                if (invoice.PaidAmount > 0)
                {
                    // مدين: حساب الخزينة (بقيمة المبلغ المدفوع)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = treasuryAccountId,
                        Description = $"تحصيل نقدي من فاتورة مبيعات رقم {invoice.InvoiceNumber}",
                        DebitAmount = invoice.PaidAmount,
                        CreditAmount = 0
                    });
                }
                
                if (invoice.RemainingAmount > 0)
                {
                    // مدين: حساب العملاء (بقيمة المبلغ المتبقي)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = customersAccountId,
                        Description = $"ذمم مدينة من فاتورة مبيعات رقم {invoice.InvoiceNumber}",
                        DebitAmount = invoice.RemainingAmount,
                        CreditAmount = 0
                    });
                }
                
                // 2. دائن: حساب المبيعات (بإجمالي قيمة الفاتورة)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = salesAccountId,
                    Description = $"إيراد مبيعات من فاتورة رقم {invoice.InvoiceNumber}",
                    DebitAmount = 0,
                    CreditAmount = invoice.TotalAmount
                });
                
                // 3. مدين: حساب تكلفة المبيعات (بتكلفة البضاعة المباعة)
                decimal costOfGoods = CalculateCostOfGoods(invoice);
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = costOfSalesAccountId,
                    Description = $"تكلفة بضاعة مباعة من فاتورة رقم {invoice.InvoiceNumber}",
                    DebitAmount = costOfGoods,
                    CreditAmount = 0
                });
                
                // 4. دائن: حساب المخزون (بتكلفة البضاعة المباعة)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = inventoryAccountId,
                    Description = $"تخفيض المخزون من فاتورة مبيعات رقم {invoice.InvoiceNumber}",
                    DebitAmount = 0,
                    CreditAmount = costOfGoods
                });
                
                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);
                
                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();
                
                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد فاتورة المبيعات: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// إنشاء قيد محاسبي لفاتورة مشتريات
        /// </summary>
        public async Task<JournalEntry> CreatePurchaseInvoiceEntryAsync(Invoice invoice, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (invoice == null)
                    throw new ArgumentNullException(nameof(invoice));
                
                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"INV-{invoice.InvoiceNumber}",
                    EntryDate = invoice.InvoiceDate,
                    Type = JournalEntryType.Invoice,
                    Description = $"قيد فاتورة مشتريات رقم {invoice.InvoiceNumber}",
                    UserId = userId,
                    InvoiceId = invoice.Id,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };
                
                // الحصول على معرفات الحسابات
                int suppliersAccountId = await GetSuppliersAccountIdAsync();
                int treasuryAccountId = await GetTreasuryAccountIdAsync();
                int inventoryAccountId = await GetInventoryAccountIdAsync();
                
                // إضافة عناصر القيد
                
                // 1. مدين: حساب المخزون (بإجمالي قيمة الفاتورة)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = inventoryAccountId,
                    Description = $"إضافة للمخزون من فاتورة مشتريات رقم {invoice.InvoiceNumber}",
                    DebitAmount = invoice.TotalAmount,
                    CreditAmount = 0
                });
                
                // 2. دائن: حساب الموردين أو الخزينة (حسب طريقة الدفع)
                if (invoice.PaidAmount > 0)
                {
                    // دائن: حساب الخزينة (بقيمة المبلغ المدفوع)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = treasuryAccountId,
                        Description = $"دفع نقدي لفاتورة مشتريات رقم {invoice.InvoiceNumber}",
                        DebitAmount = 0,
                        CreditAmount = invoice.PaidAmount
                    });
                }
                
                if (invoice.RemainingAmount > 0)
                {
                    // دائن: حساب الموردين (بقيمة المبلغ المتبقي)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = suppliersAccountId,
                        Description = $"ذمم دائنة لفاتورة مشتريات رقم {invoice.InvoiceNumber}",
                        DebitAmount = 0,
                        CreditAmount = invoice.RemainingAmount
                    });
                }
                
                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);
                
                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();
                
                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد فاتورة المشتريات: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// حساب تكلفة البضاعة المباعة
        /// </summary>
        private decimal CalculateCostOfGoods(Invoice invoice)
        {
            // في التطبيق الحقيقي، سيتم حساب تكلفة البضاعة المباعة بناءً على سعر التكلفة للمنتجات
            // هنا نفترض أن تكلفة البضاعة المباعة هي 70% من قيمة الفاتورة
            return invoice.TotalAmount * 0.7m;
        }
        
        /// <summary>
        /// إنشاء قيد محاسبي لمردودات مبيعات
        /// </summary>
        public async Task<JournalEntry> CreateSalesReturnEntryAsync(Invoice invoice, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (invoice == null)
                    throw new ArgumentNullException(nameof(invoice));
                
                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"RET-{invoice.InvoiceNumber}",
                    EntryDate = invoice.InvoiceDate,
                    Type = JournalEntryType.Invoice,
                    Description = $"قيد مردودات مبيعات رقم {invoice.InvoiceNumber}",
                    UserId = userId,
                    InvoiceId = invoice.Id,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };
                
                // الحصول على معرفات الحسابات
                int salesAccountId = await GetSalesAccountIdAsync();
                int customersAccountId = await GetCustomersAccountIdAsync();
                int treasuryAccountId = await GetTreasuryAccountIdAsync();
                int inventoryAccountId = await GetInventoryAccountIdAsync();
                int costOfSalesAccountId = await GetCostOfSalesAccountIdAsync();
                
                // إضافة عناصر القيد
                
                // 1. مدين: حساب المبيعات (بإجمالي قيمة المردودات)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = salesAccountId,
                    Description = $"تخفيض إيراد المبيعات من مردودات رقم {invoice.InvoiceNumber}",
                    DebitAmount = invoice.TotalAmount,
                    CreditAmount = 0
                });
                
                // 2. دائن: حساب العملاء أو الخزينة (حسب طريقة الدفع)
                if (invoice.PaidAmount > 0)
                {
                    // دائن: حساب الخزينة (بقيمة المبلغ المدفوع)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = treasuryAccountId,
                        Description = $"دفع نقدي لمردودات مبيعات رقم {invoice.InvoiceNumber}",
                        DebitAmount = 0,
                        CreditAmount = invoice.PaidAmount
                    });
                }
                
                if (invoice.RemainingAmount > 0)
                {
                    // دائن: حساب العملاء (بقيمة المبلغ المتبقي)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = customersAccountId,
                        Description = $"تخفيض ذمم مدينة من مردودات مبيعات رقم {invoice.InvoiceNumber}",
                        DebitAmount = 0,
                        CreditAmount = invoice.RemainingAmount
                    });
                }
                
                // 3. مدين: حساب المخزون (بتكلفة البضاعة المرتجعة)
                decimal costOfGoods = CalculateCostOfGoods(invoice);
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = inventoryAccountId,
                    Description = $"إضافة للمخزون من مردودات مبيعات رقم {invoice.InvoiceNumber}",
                    DebitAmount = costOfGoods,
                    CreditAmount = 0
                });
                
                // 4. دائن: حساب تكلفة المبيعات (بتكلفة البضاعة المرتجعة)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = costOfSalesAccountId,
                    Description = $"تخفيض تكلفة البضاعة المباعة من مردودات رقم {invoice.InvoiceNumber}",
                    DebitAmount = 0,
                    CreditAmount = costOfGoods
                });
                
                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);
                
                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();
                
                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد مردودات المبيعات: {ex.Message}");
                throw;
            }
        }
