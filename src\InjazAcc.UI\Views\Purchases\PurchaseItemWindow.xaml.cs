using System;
using System.Windows;
using System.Windows.Controls;

namespace InjazAcc.UI.Views.Purchases
{
    /// <summary>
    /// Interaction logic for PurchaseItemWindow.xaml
    /// </summary>
    public partial class PurchaseItemWindow : Window
    {
        private bool _isEditMode = false;
        private NewPurchaseItem _item;

        public NewPurchaseItem Item => _item;

        public PurchaseItemWindow()
        {
            InitializeComponent();
            _item = new NewPurchaseItem();
            InitializeControls();
        }

        public PurchaseItemWindow(NewPurchaseItem item)
        {
            InitializeComponent();
            _isEditMode = true;
            _item = item;
            InitializeControls();
            LoadItemData();
        }

        private void InitializeControls()
        {
            // تعيين القيم الافتراضية
            if (!_isEditMode)
            {
                txtCode.Text = GenerateNewCode();
                txtQuantity.Text = "1";
                txtPrice.Text = "0";
                txtDiscount.Text = "0";
                txtTotal.Text = "0";
                cmbUnit.SelectedIndex = 0;
            }

            // تغيير عنوان النافذة في حالة التعديل
            if (_isEditMode)
            {
                txtWindowTitle.Text = "تعديل صنف";
            }
        }

        private string GenerateNewCode()
        {
            // توليد رمز جديد (في التطبيق الحقيقي سيتم جلب الرمز من قاعدة البيانات)
            Random random = new Random();
            return $"P{random.Next(1000, 9999)}";
        }

        private void LoadItemData()
        {
            // تحميل بيانات الصنف في حالة التعديل
            if (_item != null)
            {
                txtCode.Text = _item.Code;
                txtName.Text = _item.Name;
                
                // تحديد الوحدة من القائمة
                foreach (ComboBoxItem item in cmbUnit.Items)
                {
                    if (item.Content.ToString() == _item.Unit)
                    {
                        cmbUnit.SelectedItem = item;
                        break;
                    }
                }
                
                txtQuantity.Text = _item.Quantity.ToString();
                txtPrice.Text = _item.Price.ToString();
                txtDiscount.Text = _item.Discount.ToString();
                txtTotal.Text = _item.Total.ToString();
            }
        }

        private void CalculateTotal()
        {
            try
            {
                // حساب الإجمالي
                if (double.TryParse(txtQuantity.Text, out double quantity) &&
                    double.TryParse(txtPrice.Text, out double price) &&
                    double.TryParse(txtDiscount.Text, out double discount))
                {
                    double total = (quantity * price) - discount;
                    txtTotal.Text = total.ToString("N2");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حساب الإجمالي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void txtQuantity_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotal();
        }

        private void txtPrice_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotal();
        }

        private void txtDiscount_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotal();
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtCode.Text))
                {
                    MessageBox.Show("الرجاء إدخال رمز الصنف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtCode.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم الصنف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtName.Focus();
                    return;
                }

                if (!double.TryParse(txtQuantity.Text, out double quantity) || quantity <= 0)
                {
                    MessageBox.Show("الرجاء إدخال كمية صحيحة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtQuantity.Focus();
                    return;
                }

                if (!double.TryParse(txtPrice.Text, out double price) || price < 0)
                {
                    MessageBox.Show("الرجاء إدخال سعر صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtPrice.Focus();
                    return;
                }

                if (!double.TryParse(txtDiscount.Text, out double discount) || discount < 0)
                {
                    MessageBox.Show("الرجاء إدخال خصم صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtDiscount.Focus();
                    return;
                }

                if (!double.TryParse(txtTotal.Text, out double total))
                {
                    MessageBox.Show("حدث خطأ في حساب الإجمالي", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // تحديث بيانات الصنف
                _item.Code = txtCode.Text;
                _item.Name = txtName.Text;
                _item.Unit = (cmbUnit.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "قطعة";
                _item.Quantity = quantity;
                _item.Price = price;
                _item.Discount = discount;
                _item.Total = total;

                // إغلاق النافذة بنجاح
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إلغاء العملية وإغلاق النافذة
            this.DialogResult = false;
        }
    }
}
