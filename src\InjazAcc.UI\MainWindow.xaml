﻿<Window x:Class="InjazAcc.UI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI"
        mc:Ignorable="d"
        Title="نظام إنجاز المحاسبي"
        Height="768" Width="1366"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        FontFamily="{materialDesign:MaterialDesignFont}"
        FlowDirection="RightToLeft"
        WindowStyle="None"
        AllowsTransparency="True"
        BorderThickness="0"
        MouseDown="Window_MouseDown">

    <Window.Effect>
        <DropShadowEffect BlurRadius="15" Direction="-90" RenderingBias="Quality" ShadowDepth="2" Color="#DDDDDD"/>
    </Window.Effect>

    <Window.Resources>
        <Style x:Key="MenuItemStyle" TargetType="ListViewItem">
            <Setter Property="Height" Value="45"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="8,2"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="materialDesign:RippleAssist.Feedback" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListViewItem">
                        <Border x:Name="Border"
                                Background="Transparent"
                                BorderThickness="0"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Grid>
                                <ContentPresenter/>
                                <Border x:Name="SelectedBorder"
                                        Background="#20FFFFFF"
                                        BorderThickness="0"
                                        CornerRadius="8"
                                        Opacity="0"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="SelectedBorder" Property="Opacity" Value="1"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#10FFFFFF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="260"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- القائمة الجانبية -->
        <Grid Grid.Column="0">
            <Grid.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#00796B" Offset="0"/>
                    <GradientStop Color="#004D40" Offset="1"/>
                </LinearGradientBrush>
            </Grid.Background>

            <Grid.RowDefinitions>
                <RowDefinition Height="140"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- شعار التطبيق والعنوان -->
            <StackPanel Grid.Row="0" VerticalAlignment="Center" HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="AccountBalance" Width="50" Height="50" HorizontalAlignment="Center" Foreground="White"/>
                <TextBlock Text="نظام إنجاز المحاسبي" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,12,0,0" Foreground="White"/>
                <TextBlock Text="الإصدار 1.0" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0" Foreground="White" Opacity="0.8"/>
            </StackPanel>

            <!-- عناصر القائمة -->
            <ListView Grid.Row="1" Background="Transparent" BorderThickness="0" Foreground="White" Margin="0,20,0,0">
                <!-- لوحة التحكم -->
                <ListViewItem x:Name="menuDashboard" Style="{StaticResource MenuItemStyle}" Selected="menuItem_Selected">
                    <Grid Width="230">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0" Width="36" Height="36" Background="#20FFFFFF" CornerRadius="8">
                            <materialDesign:PackIcon Kind="ViewDashboard" Width="20" Height="20" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Border>
                        <TextBlock Grid.Column="1" Text="لوحة التحكم" VerticalAlignment="Center" Margin="12,0,0,0" FontSize="14"/>
                    </Grid>
                </ListViewItem>

                <!-- المبيعات -->
                <ListViewItem x:Name="menuSales" Style="{StaticResource MenuItemStyle}" Selected="menuItem_Selected">
                    <Grid Width="230">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0" Width="36" Height="36" Background="#20FFFFFF" CornerRadius="8">
                            <materialDesign:PackIcon Kind="CartOutline" Width="20" Height="20" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Border>
                        <TextBlock Grid.Column="1" Text="المبيعات" VerticalAlignment="Center" Margin="12,0,0,0" FontSize="14"/>
                    </Grid>
                </ListViewItem>

                <!-- المشتريات -->
                <ListViewItem x:Name="menuPurchases" Style="{StaticResource MenuItemStyle}" Selected="menuItem_Selected">
                    <Grid Width="230">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0" Width="36" Height="36" Background="#20FFFFFF" CornerRadius="8">
                            <materialDesign:PackIcon Kind="ShoppingOutline" Width="20" Height="20" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Border>
                        <TextBlock Grid.Column="1" Text="المشتريات" VerticalAlignment="Center" Margin="12,0,0,0" FontSize="14"/>
                    </Grid>
                </ListViewItem>

                <!-- المخزون -->
                <ListViewItem x:Name="menuInventory" Style="{StaticResource MenuItemStyle}" Selected="menuItem_Selected">
                    <Grid Width="230">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0" Width="36" Height="36" Background="#20FFFFFF" CornerRadius="8">
                            <materialDesign:PackIcon Kind="PackageVariantClosed" Width="20" Height="20" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Border>
                        <TextBlock Grid.Column="1" Text="المخزون" VerticalAlignment="Center" Margin="12,0,0,0" FontSize="14"/>
                    </Grid>
                </ListViewItem>

                <!-- العملاء -->
                <ListViewItem x:Name="menuCustomers" Style="{StaticResource MenuItemStyle}" Selected="menuItem_Selected">
                    <Grid Width="230">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0" Width="36" Height="36" Background="#20FFFFFF" CornerRadius="8">
                            <materialDesign:PackIcon Kind="AccountMultipleOutline" Width="20" Height="20" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Border>
                        <TextBlock Grid.Column="1" Text="العملاء" VerticalAlignment="Center" Margin="12,0,0,0" FontSize="14"/>
                    </Grid>
                </ListViewItem>

                <!-- الموردين -->
                <ListViewItem x:Name="menuSuppliers" Style="{StaticResource MenuItemStyle}" Selected="menuItem_Selected">
                    <Grid Width="230">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0" Width="36" Height="36" Background="#20FFFFFF" CornerRadius="8">
                            <materialDesign:PackIcon Kind="TruckOutline" Width="20" Height="20" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Border>
                        <TextBlock Grid.Column="1" Text="الموردين" VerticalAlignment="Center" Margin="12,0,0,0" FontSize="14"/>
                    </Grid>
                </ListViewItem>

                <!-- الحسابات -->
                <ListViewItem x:Name="menuAccounting" Style="{StaticResource MenuItemStyle}" Selected="menuItem_Selected">
                    <Grid Width="230">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0" Width="36" Height="36" Background="#20FFFFFF" CornerRadius="8">
                            <materialDesign:PackIcon Kind="CashRegister" Width="20" Height="20" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Border>
                        <TextBlock Grid.Column="1" Text="الحسابات" VerticalAlignment="Center" Margin="12,0,0,0" FontSize="14"/>
                    </Grid>
                </ListViewItem>

                <!-- التقارير -->
                <ListViewItem x:Name="menuReports" Style="{StaticResource MenuItemStyle}" Selected="menuItem_Selected">
                    <Grid Width="230">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0" Width="36" Height="36" Background="#20FFFFFF" CornerRadius="8">
                            <materialDesign:PackIcon Kind="ChartLine" Width="20" Height="20" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Border>
                        <TextBlock Grid.Column="1" Text="التقارير" VerticalAlignment="Center" Margin="12,0,0,0" FontSize="14"/>
                    </Grid>
                </ListViewItem>

                <!-- الإعدادات -->
                <ListViewItem x:Name="menuSettings" Style="{StaticResource MenuItemStyle}" Selected="menuItem_Selected">
                    <Grid Width="230">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0" Width="36" Height="36" Background="#20FFFFFF" CornerRadius="8">
                            <materialDesign:PackIcon Kind="Cog" Width="20" Height="20" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Border>
                        <TextBlock Grid.Column="1" Text="الإعدادات" VerticalAlignment="Center" Margin="12,0,0,0" FontSize="14"/>
                    </Grid>
                </ListViewItem>
            </ListView>

            <!-- معلومات المستخدم -->
            <Border Grid.Row="2" BorderThickness="0,1,0,0" BorderBrush="#30FFFFFF" Padding="16,12" Margin="0,10,0,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <Border Grid.Column="0" Width="42" Height="42" Background="#30FFFFFF" CornerRadius="21">
                        <materialDesign:PackIcon Kind="AccountCircle" Width="28" Height="28" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White"/>
                    </Border>

                    <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="12,0,0,0">
                        <TextBlock x:Name="txtUserName" Text="المستخدم الحالي" Foreground="White" FontWeight="SemiBold" FontSize="14"/>
                        <TextBlock x:Name="txtUserRole" Text="مدير النظام" Foreground="#DDFFFFFF" FontSize="12" Margin="0,2,0,0"/>
                    </StackPanel>

                    <Button Grid.Column="2" Style="{StaticResource MaterialDesignIconButton}" Foreground="White" ToolTip="تسجيل الخروج" Click="btnLogout_Click" Width="36" Height="36" Padding="2">
                        <materialDesign:PackIcon Kind="Logout" Width="22" Height="22"/>
                    </Button>
                </Grid>
            </Border>
        </Grid>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان -->
            <Border Grid.Row="0" Background="White" BorderThickness="0,0,0,1" BorderBrush="#EEEEEE">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- أزرار التحكم في النافذة -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="16,0">
                        <Button x:Name="btnMinimize" Style="{StaticResource MaterialDesignFlatButton}" Width="30" Height="30" Padding="0" Click="btnMinimize_Click">
                            <materialDesign:PackIcon Kind="WindowMinimize" Width="16" Height="16"/>
                        </Button>
                        <Button x:Name="btnMaximize" Style="{StaticResource MaterialDesignFlatButton}" Width="30" Height="30" Padding="0" Margin="8,0" Click="btnMaximize_Click">
                            <materialDesign:PackIcon Kind="WindowMaximize" Width="16" Height="16"/>
                        </Button>
                        <Button x:Name="btnCloseMain" Style="{StaticResource MaterialDesignFlatButton}" Width="30" Height="30" Padding="0" Foreground="#E81123" Click="btnCloseMain_Click">
                            <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                        </Button>
                    </StackPanel>

                    <!-- عنوان الصفحة الحالية -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                        <Border Width="40" Height="40" Background="#F5F5F5" CornerRadius="20" Margin="0,0,12,0">
                            <materialDesign:PackIcon x:Name="currentPageIcon" Kind="ViewDashboard" Width="22" Height="22" VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        </Border>
                        <TextBlock x:Name="currentPageTitle" Text="لوحة التحكم" FontSize="18" FontWeight="SemiBold" VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- أزرار الإشعارات والمساعدة -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="0,0,16,0">
                        <Button x:Name="btnSearch" Style="{StaticResource MaterialDesignFlatButton}" ToolTip="البحث" Width="40" Height="40" Padding="0" Margin="4,0" Click="btnSearch_Click">
                            <materialDesign:PackIcon Kind="Magnify" Width="20" Height="20"/>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignFlatButton}" ToolTip="الإشعارات" Width="40" Height="40" Padding="0" Margin="4,0">
                            <Grid>
                                <materialDesign:PackIcon Kind="BellOutline" Width="20" Height="20"/>
                                <Border Width="16" Height="16" Background="#F44336" CornerRadius="8" HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,-5,-5,0">
                                    <TextBlock Text="3" FontSize="10" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </Grid>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignFlatButton}" ToolTip="المساعدة" Width="40" Height="40" Padding="0" Margin="4,0">
                            <materialDesign:PackIcon Kind="HelpCircleOutline" Width="20" Height="20"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- محتوى الصفحة -->
            <Grid Grid.Row="1">
                <Grid.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1" Opacity="0.03">
                        <GradientStop Color="#E0E0E0" Offset="0"/>
                        <GradientStop Color="#FAFAFA" Offset="1"/>
                    </LinearGradientBrush>
                </Grid.Background>

                <!-- إطار الصفحة -->
                <Border Margin="16" Background="White" CornerRadius="8"
                        materialDesign:ElevationAssist.Elevation="Dp1">
                    <Frame x:Name="mainFrame" NavigationUIVisibility="Hidden" Background="Transparent"/>
                </Border>
            </Grid>
        </Grid>
    </Grid>
</Window>
