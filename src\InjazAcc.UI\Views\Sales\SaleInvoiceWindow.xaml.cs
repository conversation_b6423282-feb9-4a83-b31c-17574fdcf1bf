using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace InjazAcc.UI.Views.Sales
{
    /// <summary>
    /// Interaction logic for SaleInvoiceWindow.xaml
    /// </summary>
    public partial class SaleInvoiceWindow : Window
    {
        private bool _isReadOnly = false;
        private SaleInvoice _invoice;

        public SaleInvoiceWindow()
        {
            InitializeComponent();
            dpInvoiceDate.SelectedDate = DateTime.Now;
            LoadSampleItems();
            CalculateTotals();
        }

        public SaleInvoiceWindow(SaleInvoice invoice, bool isReadOnly = false)
        {
            InitializeComponent();
            _invoice = invoice;
            _isReadOnly = isReadOnly;

            // تعبئة بيانات الفاتورة
            txtInvoiceTitle.Text = isReadOnly ? $"عرض فاتورة مبيعات رقم {invoice.InvoiceNumber}" : $"تعديل فاتورة مبيعات رقم {invoice.InvoiceNumber}";
            txtInvoiceNumber.Text = invoice.InvoiceNumber;
            dpInvoiceDate.SelectedDate = invoice.InvoiceDate;
            cmbCustomer.Text = invoice.CustomerName;

            // تحميل أصناف الفاتورة
            LoadSampleItems();
            CalculateTotals();

            // تعطيل التحرير في حالة العرض فقط
            if (isReadOnly)
            {
                SetReadOnlyMode();
            }
        }

        private void SetReadOnlyMode()
        {
            dpInvoiceDate.IsEnabled = false;
            cmbWarehouse.IsEnabled = false;
            cmbCustomer.IsEnabled = false;
            btnAddItem.IsEnabled = false;
            dgItems.IsReadOnly = true;
            txtNotes.IsReadOnly = true;
            btnSave.Visibility = Visibility.Collapsed;
            btnSaveAndPrint.Visibility = Visibility.Collapsed;
            btnCancel.Content = "إغلاق";
        }

        private void LoadSampleItems()
        {
            // بيانات تجريبية لأصناف الفاتورة
            var itemsList = new List<InvoiceItem>
            {
                new InvoiceItem { Code = "P001", Name = "لوح خشب متوسط", Unit = "قطعة", Quantity = 5, Price = 150.00m, DiscountPercentage = 0, TaxPercentage = 15 },
                new InvoiceItem { Code = "P002", Name = "مسامير 5 سم", Unit = "كيلو", Quantity = 2, Price = 75.50m, DiscountPercentage = 0, TaxPercentage = 15 },
                new InvoiceItem { Code = "P003", Name = "دهان أبيض", Unit = "جالون", Quantity = 3, Price = 120.25m, DiscountPercentage = 5, TaxPercentage = 15 }
            };

            // حساب القيم المشتقة
            foreach (var item in itemsList)
            {
                item.DiscountAmount = item.Quantity * item.Price * (item.DiscountPercentage / 100);
                var afterDiscount = (item.Quantity * item.Price) - item.DiscountAmount;
                item.TaxAmount = afterDiscount * (item.TaxPercentage / 100);
                item.Total = afterDiscount + item.TaxAmount;
            }

            dgItems.ItemsSource = itemsList;
        }

        private void CalculateTotals()
        {
            decimal subTotal = 0;
            decimal totalDiscount = 0;
            decimal totalTax = 0;
            decimal grandTotal = 0;

            if (dgItems.ItemsSource != null)
            {
                foreach (InvoiceItem item in dgItems.ItemsSource)
                {
                    subTotal += item.Quantity * item.Price;
                    totalDiscount += item.DiscountAmount;
                    totalTax += item.TaxAmount;
                    grandTotal += item.Total;
                }
            }

            txtSubTotal.Text = $"{subTotal:N2} ر.س";
            txtTotalDiscount.Text = $"{totalDiscount:N2} ر.س";
            txtTotalTax.Text = $"{totalTax:N2} ر.س";
            txtGrandTotal.Text = $"{grandTotal:N2} ر.س";
        }

        private void btnAddItem_Click(object sender, RoutedEventArgs e)
        {
            // فتح نافذة إضافة صنف جديد
            var itemWindow = new InvoiceItemWindow();
            if (itemWindow.ShowDialog() == true)
            {
                // إضافة الصنف الجديد إلى الفاتورة
                var items = new List<InvoiceItem>();
                if (dgItems.ItemsSource != null)
                {
                    items.AddRange(dgItems.ItemsSource as List<InvoiceItem>);
                }

                items.Add(itemWindow.Item);
                dgItems.ItemsSource = null;
                dgItems.ItemsSource = items;

                // إعادة حساب الإجماليات
                CalculateTotals();
            }
        }

        private void btnEditItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعديل الصنف المحدد
                var button = sender as Button;
                var item = button.DataContext as InvoiceItem;

                // التحقق من أن العنصر ليس فارغاً
                if (item == null)
                {
                    MessageBox.Show("خطأ: العنصر المحدد غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var itemWindow = new InvoiceItemWindow(item);
                if (itemWindow.ShowDialog() == true)
                {
                    // تحديث الصنف في الفاتورة
                    var items = new List<InvoiceItem>();
                    if (dgItems.ItemsSource != null)
                    {
                        items.AddRange(dgItems.ItemsSource as List<InvoiceItem>);
                    }

                    int index = items.FindIndex(i => i.Code == item.Code);
                    if (index >= 0)
                    {
                        items[index] = itemWindow.Item;
                    }

                    dgItems.ItemsSource = null;
                    dgItems.ItemsSource = items;

                    // إعادة حساب الإجماليات
                    CalculateTotals();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تعديل الصنف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnDeleteItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حذف الصنف المحدد
                var button = sender as Button;
                var item = button.DataContext as InvoiceItem;

                // التحقق من أن العنصر ليس فارغاً
                if (item == null)
                {
                    MessageBox.Show("خطأ: العنصر المحدد غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var result = MessageBox.Show($"هل أنت متأكد من حذف الصنف {item.Name}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // حذف الصنف من الفاتورة
                    var items = new List<InvoiceItem>();
                    if (dgItems.ItemsSource != null)
                    {
                        items.AddRange(dgItems.ItemsSource as List<InvoiceItem>);
                    }

                    items.RemoveAll(i => i.Code == item.Code);

                    dgItems.ItemsSource = null;
                    dgItems.ItemsSource = items;

                    // إعادة حساب الإجماليات
                    CalculateTotals();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف الصنف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            // حفظ الفاتورة
            SaveInvoice();
        }

        private void btnSaveAndPrint_Click(object sender, RoutedEventArgs e)
        {
            // حفظ وطباعة الفاتورة
            if (SaveInvoice())
            {
                MessageBox.Show("جاري طباعة الفاتورة...", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private bool SaveInvoice()
        {
            // التحقق من صحة البيانات
            if (dpInvoiceDate.SelectedDate == null)
            {
                MessageBox.Show("الرجاء تحديد تاريخ الفاتورة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

            if (string.IsNullOrEmpty(cmbCustomer.Text))
            {
                MessageBox.Show("الرجاء اختيار العميل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

            if (dgItems.Items.Count == 0)
            {
                MessageBox.Show("الرجاء إضافة صنف واحد على الأقل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

            // حفظ الفاتورة في قاعدة البيانات
            MessageBox.Show("تم حفظ الفاتورة بنجاح", "تم", MessageBoxButton.OK, MessageBoxImage.Information);

            // إغلاق النافذة
            this.DialogResult = true;
            this.Close();

            return true;
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق النافذة
            this.DialogResult = false;
            this.Close();
        }

        /// <summary>
        /// تحريك النافذة عند الضغط على الماوس
        /// </summary>
        private void Window_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (e.ButtonState == MouseButtonState.Pressed)
                {
                    DragMove();
                }
            }
            catch (Exception ex)
            {
                // تجاهل الخطأ لتجنب توقف التطبيق
                Console.WriteLine($"خطأ في تحريك النافذة: {ex.Message}");
            }
        }
    }

    // فئة بيانات صنف الفاتورة
    public class InvoiceItem
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Unit { get; set; }
        public int Quantity { get; set; }
        public decimal Price { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxPercentage { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal Total { get; set; }
    }

    public class SaleInvoice
    {
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public string CustomerName { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public string Status { get; set; }
    }
}
