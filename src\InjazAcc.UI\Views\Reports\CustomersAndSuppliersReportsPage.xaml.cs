using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using InjazAcc.Core.Exceptions;
using InjazAcc.Services.Helpers;

namespace InjazAcc.UI.Views.Reports
{
    /// <summary>
    /// Interaction logic for CustomersAndSuppliersReportsPage.xaml
    /// </summary>
    public partial class CustomersAndSuppliersReportsPage : Page
    {
        private ObservableCollection<CustomerSupplierReportItem> _reportItems;

        public CustomersAndSuppliersReportsPage()
        {
            try
            {
                // تهيئة البيانات قبل تهيئة واجهة المستخدم
                _reportItems = new ObservableCollection<CustomerSupplierReportItem>();

                InitializeComponent();

                // تعيين التواريخ الافتراضية (الشهر الحالي)
                if (dpFromDate != null && dpToDate != null)
                {
                    dpFromDate.SelectedDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                    dpToDate.SelectedDate = DateTime.Now;
                }
                else
                {
                    // استخدام ملحقات التحقق من صحة البيانات
                    throw new ValidationException("عناصر التاريخ غير متاحة", ErrorCodes.RequiredValueMissing);
                }

                // ربط البيانات بالجدول
                if (dgReport != null)
                {
                    dgReport.ItemsSource = _reportItems;
                }
                else
                {
                    // استخدام ملحقات التحقق من صحة البيانات
                    throw new ValidationException("عنصر الجدول غير متاح", ErrorCodes.RequiredValueMissing);
                }

                // تحديث واجهة المستخدم بناءً على نوع التقرير المحدد
                UpdateUIBasedOnReportType();
            }
            catch (Exception ex)
            {
                // استخدام نظام معالجة الاستثناءات الجديد
                ex.Handle(true);
            }
        }

        private void cmbReportType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // التحقق من وجود عناصر واجهة المستخدم باستخدام ملحقات التحقق
                cmbReportType.ThrowIfNull("cmbReportType", "عنصر نوع التقرير غير متاح");
                dgReport.ThrowIfNull("dgReport", "عنصر الجدول غير متاح");

                // تحديث واجهة المستخدم بناءً على نوع التقرير المحدد
                UpdateUIBasedOnReportType();

                // تغيير أعمدة الجدول حسب نوع التقرير
                if (cmbReportType.SelectedIndex >= 0)
                {
                    switch (cmbReportType.SelectedIndex)
                    {
                        case 0: // كشف حساب عميل
                        case 1: // كشف حساب مورد
                            ConfigureStatementReportColumns();
                            break;
                        case 2: // أعمار ديون العملاء
                        case 3: // أعمار ديون الموردين
                            ConfigureAgingReportColumns();
                            break;
                        case 4: // تقرير أرصدة العملاء
                        case 5: // تقرير أرصدة الموردين
                            ConfigureBalancesReportColumns();
                            break;
                        default:
                            ConfigureStatementReportColumns(); // الحالة الافتراضية
                            break;
                    }
                }
                else
                {
                    // إذا لم يكن هناك عنصر محدد، استخدم التكوين الافتراضي
                    ConfigureStatementReportColumns();
                }
            }
            catch (ValidationException ex)
            {
                // معالجة استثناءات التحقق من صحة البيانات
                ex.Handle(true);
            }
            catch (Exception ex)
            {
                // تحويل الاستثناء العام إلى استثناء مخصص
                ex.ToInjazException("حدث خطأ أثناء تغيير نوع التقرير").Handle(true);
            }
        }

        private void UpdateUIBasedOnReportType()
        {
            try
            {
                // التحقق من وجود عناصر واجهة المستخدم باستخدام ملحقات التحقق
                cmbReportType.ThrowIfNull("cmbReportType", "عنصر نوع التقرير غير متاح");
                lblCustomerSupplier.ThrowIfNull("lblCustomerSupplier", "عنصر تسمية العميل/المورد غير متاح");
                cmbCustomerSupplier.ThrowIfNull("cmbCustomerSupplier", "عنصر قائمة العملاء/الموردين غير متاح");

                // التحقق من وجود قيمة محددة في قائمة نوع التقرير
                if (cmbReportType.SelectedIndex < 0)
                {
                    cmbReportType.SelectedIndex = 0; // تعيين القيمة الافتراضية
                }

                // تحديث عنوان حقل العميل/المورد
                if (cmbReportType.SelectedIndex == 0 || cmbReportType.SelectedIndex == 2 || cmbReportType.SelectedIndex == 4)
                {
                    lblCustomerSupplier.Text = "العميل:";

                    // تحديث قائمة العملاء
                    cmbCustomerSupplier.Items.Clear();
                    cmbCustomerSupplier.Items.Add(new ComboBoxItem { Content = "الكل" });
                    cmbCustomerSupplier.Items.Add(new ComboBoxItem { Content = "شركة الأمل للتجارة" });
                    cmbCustomerSupplier.Items.Add(new ComboBoxItem { Content = "مؤسسة النور" });
                    cmbCustomerSupplier.Items.Add(new ComboBoxItem { Content = "شركة الصفا للمقاولات" });
                    cmbCustomerSupplier.Items.Add(new ComboBoxItem { Content = "مؤسسة الإبداع" });
                    cmbCustomerSupplier.Items.Add(new ComboBoxItem { Content = "شركة الوفاء" });
                }
                else
                {
                    lblCustomerSupplier.Text = "المورد:";

                    // تحديث قائمة الموردين
                    cmbCustomerSupplier.Items.Clear();
                    cmbCustomerSupplier.Items.Add(new ComboBoxItem { Content = "الكل" });
                    cmbCustomerSupplier.Items.Add(new ComboBoxItem { Content = "شركة التوريدات العامة" });
                    cmbCustomerSupplier.Items.Add(new ComboBoxItem { Content = "مؤسسة الإمداد" });
                    cmbCustomerSupplier.Items.Add(new ComboBoxItem { Content = "شركة المواد الأولية" });
                    cmbCustomerSupplier.Items.Add(new ComboBoxItem { Content = "مؤسسة التجهيزات" });
                    cmbCustomerSupplier.Items.Add(new ComboBoxItem { Content = "شركة الإنتاج" });
                }

                // تعيين العنصر الأول كقيمة افتراضية
                if (cmbCustomerSupplier.Items.Count > 0)
                {
                    cmbCustomerSupplier.SelectedIndex = 0;
                }
            }
            catch (ValidationException ex)
            {
                // معالجة استثناءات التحقق من صحة البيانات
                ex.Handle(true);
            }
            catch (Exception ex)
            {
                // تحويل الاستثناء العام إلى استثناء مخصص
                ex.ToInjazException("حدث خطأ أثناء تحديث واجهة المستخدم").Handle(true);
            }
        }

        private void ConfigureStatementReportColumns()
        {
            try
            {
                // التحقق من وجود عنصر الجدول
                if (dgReport == null)
                {
                    MessageBox.Show("لم يتم تهيئة عنصر الجدول بشكل صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return; // الخروج إذا كان العنصر غير موجود
                }

                // تكوين أعمدة كشف الحساب
                dgReport.Columns.Clear();

                dgReport.Columns.Add(new DataGridTextColumn { Header = "التاريخ", Binding = new System.Windows.Data.Binding("Date") { StringFormat = "dd/MM/yyyy" }, Width = new DataGridLength(100) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "رقم المستند", Binding = new System.Windows.Data.Binding("DocumentNumber"), Width = new DataGridLength(100) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "نوع المستند", Binding = new System.Windows.Data.Binding("DocumentType"), Width = new DataGridLength(120) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "البيان", Binding = new System.Windows.Data.Binding("Description"), Width = new DataGridLength(1, DataGridLengthUnitType.Star) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "مدين", Binding = new System.Windows.Data.Binding("Debit") { StringFormat = "N2" }, Width = new DataGridLength(120) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "دائن", Binding = new System.Windows.Data.Binding("Credit") { StringFormat = "N2" }, Width = new DataGridLength(120) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "الرصيد", Binding = new System.Windows.Data.Binding("Balance") { StringFormat = "N2" }, Width = new DataGridLength(120) });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تكوين أعمدة التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ConfigureAgingReportColumns()
        {
            try
            {
                // التحقق من وجود عنصر الجدول
                if (dgReport == null)
                {
                    MessageBox.Show("لم يتم تهيئة عنصر الجدول بشكل صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return; // الخروج إذا كان العنصر غير موجود
                }

                // تكوين أعمدة تقرير أعمار الديون
                dgReport.Columns.Clear();

                dgReport.Columns.Add(new DataGridTextColumn { Header = "الكود", Binding = new System.Windows.Data.Binding("Code"), Width = new DataGridLength(100) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "الاسم", Binding = new System.Windows.Data.Binding("Name"), Width = new DataGridLength(1, DataGridLengthUnitType.Star) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "الرصيد الإجمالي", Binding = new System.Windows.Data.Binding("TotalBalance") { StringFormat = "N2" }, Width = new DataGridLength(120) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "حالي", Binding = new System.Windows.Data.Binding("Current") { StringFormat = "N2" }, Width = new DataGridLength(120) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "1-30 يوم", Binding = new System.Windows.Data.Binding("Days30") { StringFormat = "N2" }, Width = new DataGridLength(120) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "31-60 يوم", Binding = new System.Windows.Data.Binding("Days60") { StringFormat = "N2" }, Width = new DataGridLength(120) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "61-90 يوم", Binding = new System.Windows.Data.Binding("Days90") { StringFormat = "N2" }, Width = new DataGridLength(120) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "أكثر من 90 يوم", Binding = new System.Windows.Data.Binding("DaysOver90") { StringFormat = "N2" }, Width = new DataGridLength(120) });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تكوين أعمدة التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ConfigureBalancesReportColumns()
        {
            try
            {
                // التحقق من وجود عنصر الجدول
                if (dgReport == null)
                {
                    MessageBox.Show("لم يتم تهيئة عنصر الجدول بشكل صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return; // الخروج إذا كان العنصر غير موجود
                }

                // تكوين أعمدة تقرير الأرصدة
                dgReport.Columns.Clear();

                dgReport.Columns.Add(new DataGridTextColumn { Header = "الكود", Binding = new System.Windows.Data.Binding("Code"), Width = new DataGridLength(100) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "الاسم", Binding = new System.Windows.Data.Binding("Name"), Width = new DataGridLength(1, DataGridLengthUnitType.Star) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "الهاتف", Binding = new System.Windows.Data.Binding("Phone"), Width = new DataGridLength(120) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي المبيعات", Binding = new System.Windows.Data.Binding("TotalSales") { StringFormat = "N2" }, Width = new DataGridLength(120) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي المدفوعات", Binding = new System.Windows.Data.Binding("TotalPayments") { StringFormat = "N2" }, Width = new DataGridLength(120) });
                dgReport.Columns.Add(new DataGridTextColumn { Header = "الرصيد", Binding = new System.Windows.Data.Binding("Balance") { StringFormat = "N2" }, Width = new DataGridLength(120) });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تكوين أعمدة التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnShowReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحميل بيانات تجريبية
                LoadSampleData();

                // حساب الإجماليات
                CalculateTotals();

                // استخدام استثناء معلومات لعرض رسالة نجاح
                var successMessage = new InjazAccException("تم تحميل التقرير بنجاح", "INFO-001", ErrorSeverity.Information);
                successMessage.Handle(true);
            }
            catch (ValidationException ex)
            {
                // معالجة استثناءات التحقق من صحة البيانات
                ex.Handle(true);
            }
            catch (Exception ex)
            {
                // تحويل الاستثناء العام إلى استثناء مخصص
                ex.ToInjazException("حدث خطأ أثناء عرض التقرير").Handle(true);
            }
        }

        private void LoadSampleData()
        {
            try
            {
                // التحقق من وجود مجموعة البيانات
                if (_reportItems == null)
                {
                    _reportItems = new ObservableCollection<CustomerSupplierReportItem>();

                    // التحقق من وجود عنصر الجدول باستخدام ملحقات التحقق
                    dgReport.ThrowIfNull("dgReport", "عنصر الجدول غير متاح");
                    dgReport.ItemsSource = _reportItems;
                }

                _reportItems.Clear();

                // التحقق من وجود عنصر نوع التقرير باستخدام ملحقات التحقق
                cmbReportType.ThrowIfNull("cmbReportType", "عنصر نوع التقرير غير متاح");

                // بيانات تجريبية حسب نوع التقرير
                if (cmbReportType.SelectedIndex >= 0)
                {
                    switch (cmbReportType.SelectedIndex)
                    {
                        case 0: // كشف حساب عميل
                            LoadCustomerStatementData();
                            break;
                        case 1: // كشف حساب مورد
                            LoadSupplierStatementData();
                            break;
                        case 2: // أعمار ديون العملاء
                            LoadCustomerAgingData();
                            break;
                        case 3: // أعمار ديون الموردين
                            LoadSupplierAgingData();
                            break;
                        case 4: // تقرير أرصدة العملاء
                            LoadCustomerBalancesData();
                            break;
                        case 5: // تقرير أرصدة الموردين
                            LoadSupplierBalancesData();
                            break;
                        default:
                            LoadCustomerStatementData(); // الحالة الافتراضية
                            break;
                    }
                }
                else
                {
                    // إذا لم يكن هناك عنصر محدد، استخدم البيانات الافتراضية
                    LoadCustomerStatementData();
                }
            }
            catch (ValidationException ex)
            {
                // معالجة استثناءات التحقق من صحة البيانات
                ex.Handle(true);
            }
            catch (Exception ex)
            {
                // تحويل الاستثناء العام إلى استثناء مخصص
                ex.ToInjazException("حدث خطأ أثناء تحميل البيانات").Handle(true);
            }
        }

        private void LoadCustomerStatementData()
        {
            // بيانات تجريبية لكشف حساب عميل
            _reportItems.Add(new CustomerSupplierReportItem { Date = new DateTime(2023, 1, 1), DocumentNumber = "INV-001", DocumentType = "فاتورة مبيعات", Description = "فاتورة مبيعات نقدية", Debit = 5000, Credit = 0, Balance = 5000 });
            _reportItems.Add(new CustomerSupplierReportItem { Date = new DateTime(2023, 1, 5), DocumentNumber = "REC-001", DocumentType = "سند قبض", Description = "دفعة نقدية", Debit = 0, Credit = 3000, Balance = 2000 });
            _reportItems.Add(new CustomerSupplierReportItem { Date = new DateTime(2023, 1, 10), DocumentNumber = "INV-002", DocumentType = "فاتورة مبيعات", Description = "فاتورة مبيعات آجلة", Debit = 8000, Credit = 0, Balance = 10000 });
            _reportItems.Add(new CustomerSupplierReportItem { Date = new DateTime(2023, 1, 15), DocumentNumber = "RET-001", DocumentType = "مرتجع مبيعات", Description = "مرتجع بضاعة تالفة", Debit = 0, Credit = 1000, Balance = 9000 });
            _reportItems.Add(new CustomerSupplierReportItem { Date = new DateTime(2023, 1, 20), DocumentNumber = "REC-002", DocumentType = "سند قبض", Description = "دفعة شيك", Debit = 0, Credit = 4000, Balance = 5000 });
            _reportItems.Add(new CustomerSupplierReportItem { Date = new DateTime(2023, 1, 25), DocumentNumber = "INV-003", DocumentType = "فاتورة مبيعات", Description = "فاتورة مبيعات آجلة", Debit = 12000, Credit = 0, Balance = 17000 });
            _reportItems.Add(new CustomerSupplierReportItem { Date = new DateTime(2023, 1, 30), DocumentNumber = "REC-003", DocumentType = "سند قبض", Description = "دفعة تحويل بنكي", Debit = 0, Credit = 7000, Balance = 10000 });
        }

        private void LoadSupplierStatementData()
        {
            // بيانات تجريبية لكشف حساب مورد
            _reportItems.Add(new CustomerSupplierReportItem { Date = new DateTime(2023, 1, 1), DocumentNumber = "PUR-001", DocumentType = "فاتورة مشتريات", Description = "فاتورة مشتريات نقدية", Debit = 0, Credit = 8000, Balance = -8000 });
            _reportItems.Add(new CustomerSupplierReportItem { Date = new DateTime(2023, 1, 5), DocumentNumber = "PAY-001", DocumentType = "سند صرف", Description = "دفعة نقدية", Debit = 5000, Credit = 0, Balance = -3000 });
            _reportItems.Add(new CustomerSupplierReportItem { Date = new DateTime(2023, 1, 10), DocumentNumber = "PUR-002", DocumentType = "فاتورة مشتريات", Description = "فاتورة مشتريات آجلة", Debit = 0, Credit = 12000, Balance = -15000 });
            _reportItems.Add(new CustomerSupplierReportItem { Date = new DateTime(2023, 1, 15), DocumentNumber = "RET-001", DocumentType = "مرتجع مشتريات", Description = "مرتجع بضاعة تالفة", Debit = 2000, Credit = 0, Balance = -13000 });
            _reportItems.Add(new CustomerSupplierReportItem { Date = new DateTime(2023, 1, 20), DocumentNumber = "PAY-002", DocumentType = "سند صرف", Description = "دفعة شيك", Debit = 6000, Credit = 0, Balance = -7000 });
            _reportItems.Add(new CustomerSupplierReportItem { Date = new DateTime(2023, 1, 25), DocumentNumber = "PUR-003", DocumentType = "فاتورة مشتريات", Description = "فاتورة مشتريات آجلة", Debit = 0, Credit = 9000, Balance = -16000 });
            _reportItems.Add(new CustomerSupplierReportItem { Date = new DateTime(2023, 1, 30), DocumentNumber = "PAY-003", DocumentType = "سند صرف", Description = "دفعة تحويل بنكي", Debit = 10000, Credit = 0, Balance = -6000 });
        }

        private void LoadCustomerAgingData()
        {
            // بيانات تجريبية لأعمار ديون العملاء
            _reportItems.Add(new CustomerSupplierReportItem { Code = "C001", Name = "شركة الأمل للتجارة", TotalBalance = 15000, Current = 5000, Days30 = 4000, Days60 = 3000, Days90 = 2000, DaysOver90 = 1000 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "C002", Name = "مؤسسة النور", TotalBalance = 8000, Current = 3000, Days30 = 2000, Days60 = 1500, Days90 = 1000, DaysOver90 = 500 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "C003", Name = "شركة الصفا للمقاولات", TotalBalance = 22000, Current = 8000, Days30 = 6000, Days60 = 4000, Days90 = 3000, DaysOver90 = 1000 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "C004", Name = "مؤسسة الإبداع", TotalBalance = 12000, Current = 4000, Days30 = 3000, Days60 = 2500, Days90 = 1500, DaysOver90 = 1000 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "C005", Name = "شركة الوفاء", TotalBalance = 18000, Current = 6000, Days30 = 5000, Days60 = 4000, Days90 = 2000, DaysOver90 = 1000 });
        }

        private void LoadSupplierAgingData()
        {
            // بيانات تجريبية لأعمار ديون الموردين
            _reportItems.Add(new CustomerSupplierReportItem { Code = "S001", Name = "شركة التوريدات العامة", TotalBalance = 25000, Current = 10000, Days30 = 8000, Days60 = 4000, Days90 = 2000, DaysOver90 = 1000 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "S002", Name = "مؤسسة الإمداد", TotalBalance = 15000, Current = 6000, Days30 = 4000, Days60 = 3000, Days90 = 1500, DaysOver90 = 500 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "S003", Name = "شركة المواد الأولية", TotalBalance = 32000, Current = 12000, Days30 = 10000, Days60 = 6000, Days90 = 3000, DaysOver90 = 1000 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "S004", Name = "مؤسسة التجهيزات", TotalBalance = 18000, Current = 7000, Days30 = 5000, Days60 = 3000, Days90 = 2000, DaysOver90 = 1000 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "S005", Name = "شركة الإنتاج", TotalBalance = 28000, Current = 10000, Days30 = 8000, Days60 = 6000, Days90 = 3000, DaysOver90 = 1000 });
        }

        private void LoadCustomerBalancesData()
        {
            // بيانات تجريبية لأرصدة العملاء
            _reportItems.Add(new CustomerSupplierReportItem { Code = "C001", Name = "شركة الأمل للتجارة", Phone = "0555123456", TotalSales = 50000, TotalPayments = 35000, Balance = 15000 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "C002", Name = "مؤسسة النور", Phone = "0555234567", TotalSales = 30000, TotalPayments = 22000, Balance = 8000 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "C003", Name = "شركة الصفا للمقاولات", Phone = "0555345678", TotalSales = 80000, TotalPayments = 58000, Balance = 22000 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "C004", Name = "مؤسسة الإبداع", Phone = "0555456789", TotalSales = 45000, TotalPayments = 33000, Balance = 12000 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "C005", Name = "شركة الوفاء", Phone = "0555567890", TotalSales = 65000, TotalPayments = 47000, Balance = 18000 });
        }

        private void LoadSupplierBalancesData()
        {
            // بيانات تجريبية لأرصدة الموردين
            _reportItems.Add(new CustomerSupplierReportItem { Code = "S001", Name = "شركة التوريدات العامة", Phone = "0555123456", TotalSales = 85000, TotalPayments = 60000, Balance = 25000 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "S002", Name = "مؤسسة الإمداد", Phone = "0555234567", TotalSales = 55000, TotalPayments = 40000, Balance = 15000 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "S003", Name = "شركة المواد الأولية", Phone = "0555345678", TotalSales = 120000, TotalPayments = 88000, Balance = 32000 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "S004", Name = "مؤسسة التجهيزات", Phone = "0555456789", TotalSales = 65000, TotalPayments = 47000, Balance = 18000 });
            _reportItems.Add(new CustomerSupplierReportItem { Code = "S005", Name = "شركة الإنتاج", Phone = "0555567890", TotalSales = 95000, TotalPayments = 67000, Balance = 28000 });
        }

        private void CalculateTotals()
        {
            try
            {
                // التحقق من وجود عنصر نوع التقرير باستخدام ملحقات التحقق
                cmbReportType.ThrowIfNull("cmbReportType", "عنصر نوع التقرير غير متاح");

                // حساب الإجماليات حسب نوع التقرير
                if (cmbReportType.SelectedIndex >= 0)
                {
                    switch (cmbReportType.SelectedIndex)
                    {
                        case 0: // كشف حساب عميل
                        case 1: // كشف حساب مورد
                            CalculateStatementTotals();
                            break;
                        case 2: // أعمار ديون العملاء
                        case 3: // أعمار ديون الموردين
                            CalculateAgingTotals();
                            break;
                        case 4: // تقرير أرصدة العملاء
                        case 5: // تقرير أرصدة الموردين
                            CalculateBalancesTotals();
                            break;
                        default:
                            CalculateStatementTotals(); // الحالة الافتراضية
                            break;
                    }
                }
                else
                {
                    // إذا لم يكن هناك عنصر محدد، استخدم الإجماليات الافتراضية
                    CalculateStatementTotals();
                }
            }
            catch (ValidationException ex)
            {
                // معالجة استثناءات التحقق من صحة البيانات
                ex.Handle(true);
            }
            catch (Exception ex)
            {
                // تحويل الاستثناء العام إلى استثناء مخصص
                ex.ToInjazException("حدث خطأ أثناء حساب الإجماليات").Handle(true);
            }
        }

        private void CalculateStatementTotals()
        {
            try
            {
                // التحقق من وجود عناصر واجهة المستخدم باستخدام ملحقات التحقق
                txtTotalDebit.ThrowIfNull("txtTotalDebit", "عنصر إجمالي المدين غير متاح");
                txtTotalCredit.ThrowIfNull("txtTotalCredit", "عنصر إجمالي الدائن غير متاح");
                txtBalance.ThrowIfNull("txtBalance", "عنصر الرصيد غير متاح");

                double totalDebit = 0;
                double totalCredit = 0;
                double balance = 0;

                // التحقق من وجود البيانات
                if (_reportItems != null)
                {
                    foreach (var item in _reportItems)
                    {
                        totalDebit += item.Debit;
                        totalCredit += item.Credit;
                    }
                }
                else
                {
                    // إذا كانت البيانات غير موجودة، نرمي استثناء
                    throw new BusinessException("لا توجد بيانات لحساب الإجماليات", ErrorCodes.BusinessRuleViolation);
                }

                balance = totalDebit - totalCredit;

                // عرض الإجماليات
                txtTotalDebit.Text = totalDebit.ToString("N2");
                txtTotalCredit.Text = totalCredit.ToString("N2");
                txtBalance.Text = balance.ToString("N2");
            }
            catch (ValidationException ex)
            {
                // معالجة استثناءات التحقق من صحة البيانات
                ex.Handle(true);
            }
            catch (BusinessException ex)
            {
                // معالجة استثناءات العمليات التجارية
                ex.Handle(true);
            }
            catch (Exception ex)
            {
                // تحويل الاستثناء العام إلى استثناء مخصص
                ex.ToInjazException("حدث خطأ أثناء حساب إجماليات كشف الحساب").Handle(true);
            }
        }

        private void CalculateAgingTotals()
        {
            try
            {
                // التحقق من وجود عناصر واجهة المستخدم باستخدام ملحقات التحقق
                txtTotalDebit.ThrowIfNull("txtTotalDebit", "عنصر إجمالي المدين غير متاح");
                txtTotalCredit.ThrowIfNull("txtTotalCredit", "عنصر إجمالي الدائن غير متاح");
                txtBalance.ThrowIfNull("txtBalance", "عنصر الرصيد غير متاح");

                double totalBalance = 0;

                // التحقق من وجود البيانات
                if (_reportItems != null)
                {
                    foreach (var item in _reportItems)
                    {
                        totalBalance += item.TotalBalance;
                    }
                }
                else
                {
                    // إذا كانت البيانات غير موجودة، نرمي استثناء
                    throw new BusinessException("لا توجد بيانات لحساب إجماليات أعمار الديون", ErrorCodes.BusinessRuleViolation);
                }

                // عرض الإجماليات
                txtTotalDebit.Text = "0.00";
                txtTotalCredit.Text = "0.00";
                txtBalance.Text = totalBalance.ToString("N2");
            }
            catch (ValidationException ex)
            {
                // معالجة استثناءات التحقق من صحة البيانات
                ex.Handle(true);
            }
            catch (BusinessException ex)
            {
                // معالجة استثناءات العمليات التجارية
                ex.Handle(true);
            }
            catch (Exception ex)
            {
                // تحويل الاستثناء العام إلى استثناء مخصص
                ex.ToInjazException("حدث خطأ أثناء حساب إجماليات أعمار الديون").Handle(true);
            }
        }

        private void CalculateBalancesTotals()
        {
            try
            {
                // التحقق من وجود عناصر واجهة المستخدم باستخدام ملحقات التحقق
                txtTotalDebit.ThrowIfNull("txtTotalDebit", "عنصر إجمالي المدين غير متاح");
                txtTotalCredit.ThrowIfNull("txtTotalCredit", "عنصر إجمالي الدائن غير متاح");
                txtBalance.ThrowIfNull("txtBalance", "عنصر الرصيد غير متاح");

                double totalSales = 0;
                double totalPayments = 0;
                double totalBalance = 0;

                // التحقق من وجود البيانات
                if (_reportItems != null)
                {
                    foreach (var item in _reportItems)
                    {
                        totalSales += item.TotalSales;
                        totalPayments += item.TotalPayments;
                        totalBalance += item.Balance;
                    }
                }
                else
                {
                    // إذا كانت البيانات غير موجودة، نرمي استثناء
                    throw new BusinessException("لا توجد بيانات لحساب إجماليات الأرصدة", ErrorCodes.BusinessRuleViolation);
                }

                // عرض الإجماليات
                txtTotalDebit.Text = totalSales.ToString("N2");
                txtTotalCredit.Text = totalPayments.ToString("N2");
                txtBalance.Text = totalBalance.ToString("N2");
            }
            catch (ValidationException ex)
            {
                // معالجة استثناءات التحقق من صحة البيانات
                ex.Handle(true);
            }
            catch (BusinessException ex)
            {
                // معالجة استثناءات العمليات التجارية
                ex.Handle(true);
            }
            catch (Exception ex)
            {
                // تحويل الاستثناء العام إلى استثناء مخصص
                ex.ToInjazException("حدث خطأ أثناء حساب إجماليات الأرصدة").Handle(true);
            }
        }

        private void btnPrintReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود بيانات للطباعة
                if (_reportItems == null || _reportItems.Count == 0)
                {
                    throw new BusinessException("لا توجد بيانات للطباعة", ErrorCodes.BusinessRuleViolation);
                }

                // استخدام استثناء معلومات لعرض رسالة
                var infoMessage = new InjazAccException("جاري طباعة التقرير...", "INFO-002", ErrorSeverity.Information);
                infoMessage.Handle(true);

                // هنا يمكن إضافة كود الطباعة الفعلي
            }
            catch (BusinessException ex)
            {
                // معالجة استثناءات العمليات التجارية
                ex.Handle(true);
            }
            catch (Exception ex)
            {
                // تحويل الاستثناء العام إلى استثناء مخصص
                ex.ToInjazException("حدث خطأ أثناء طباعة التقرير").Handle(true);
            }
        }

        private void btnExportExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود بيانات للتصدير
                if (_reportItems == null || _reportItems.Count == 0)
                {
                    throw new BusinessException("لا توجد بيانات للتصدير إلى Excel", ErrorCodes.BusinessRuleViolation);
                }

                // استخدام استثناء معلومات لعرض رسالة
                var infoMessage = new InjazAccException("جاري تصدير التقرير إلى Excel...", "INFO-003", ErrorSeverity.Information);
                infoMessage.Handle(true);

                // هنا يمكن إضافة كود التصدير الفعلي
            }
            catch (BusinessException ex)
            {
                // معالجة استثناءات العمليات التجارية
                ex.Handle(true);
            }
            catch (Exception ex)
            {
                // تحويل الاستثناء العام إلى استثناء مخصص
                ex.ToInjazException("حدث خطأ أثناء تصدير التقرير إلى Excel").Handle(true);
            }
        }

        private void btnExportPDF_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود بيانات للتصدير
                if (_reportItems == null || _reportItems.Count == 0)
                {
                    throw new BusinessException("لا توجد بيانات للتصدير إلى PDF", ErrorCodes.BusinessRuleViolation);
                }

                // استخدام استثناء معلومات لعرض رسالة
                var infoMessage = new InjazAccException("جاري تصدير التقرير إلى PDF...", "INFO-004", ErrorSeverity.Information);
                infoMessage.Handle(true);

                // هنا يمكن إضافة كود التصدير الفعلي
            }
            catch (BusinessException ex)
            {
                // معالجة استثناءات العمليات التجارية
                ex.Handle(true);
            }
            catch (Exception ex)
            {
                // تحويل الاستثناء العام إلى استثناء مخصص
                ex.ToInjazException("حدث خطأ أثناء تصدير التقرير إلى PDF").Handle(true);
            }
        }

        private void btnBackToReports_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // العودة إلى صفحة التقارير الرئيسية
                if (NavigationService != null)
                {
                    NavigationService.Navigate(new ReportsPage());
                }
                else
                {
                    MessageBox.Show("لا يمكن العودة إلى صفحة التقارير", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                // تحويل الاستثناء العام إلى استثناء مخصص
                ex.ToInjazException("حدث خطأ أثناء العودة لصفحة التقارير").Handle(true);
            }
        }
    }

    public class CustomerSupplierReportItem
    {
        public DateTime Date { get; set; }
        public string DocumentNumber { get; set; } = "";
        public string DocumentType { get; set; } = "";
        public string Description { get; set; } = "";
        public double Debit { get; set; }
        public double Credit { get; set; }
        public double Balance { get; set; }

        public string Code { get; set; } = "";
        public string Name { get; set; } = "";
        public double TotalBalance { get; set; }
        public double Current { get; set; }
        public double Days30 { get; set; }
        public double Days60 { get; set; }
        public double Days90 { get; set; }
        public double DaysOver90 { get; set; }

        public string Phone { get; set; } = "";
        public double TotalSales { get; set; }
        public double TotalPayments { get; set; }
    }
}
