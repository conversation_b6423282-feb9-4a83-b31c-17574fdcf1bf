using System;
using System.Windows;
using System.Windows.Controls;
using InjazAcc.Core.Exceptions;
using InjazAcc.Services.Helpers;

namespace InjazAcc.UI.Views.Sales
{
    /// <summary>
    /// Interaction logic for SaleItemWindow.xaml
    /// </summary>
    public partial class SaleItemWindow : Window
    {
        private bool _isEditMode = false;
        private NewInvoiceItem _item;

        public NewInvoiceItem Item => _item;

        public SaleItemWindow()
        {
            InitializeComponent();
            _item = new NewInvoiceItem();
            InitializeControls();
        }

        public SaleItemWindow(NewInvoiceItem item)
        {
            InitializeComponent();
            _isEditMode = true;
            _item = item;
            InitializeControls();
            LoadItemData();
        }

        private void InitializeControls()
        {
            try
            {
                // تعيين القيم الافتراضية
                if (!_isEditMode)
                {
                    txtCode.Text = GenerateNewCode();
                    txtQuantity.Text = "1";
                    txtPurchasePrice.Text = "0";
                    txtPrice.Text = "0";
                    txtDiscount.Text = "0";
                    txtTotal.Text = "0";
                    cmbUnit.SelectedIndex = 0;
                }

                // تغيير عنوان النافذة في حالة التعديل
                if (_isEditMode)
                {
                    txtWindowTitle.Text = "تعديل منتج";
                    this.Title = "تعديل منتج";
                }
            }
            catch (Exception ex)
            {
                ex.ToInjazException("حدث خطأ أثناء تهيئة النافذة").Handle(true);
            }
        }

        private string GenerateNewCode()
        {
            // توليد رمز جديد (في التطبيق الحقيقي سيتم جلب الرمز من قاعدة البيانات)
            Random random = new Random();
            return $"P{random.Next(1000, 9999)}";
        }

        private void LoadItemData()
        {
            try
            {
                // تحميل بيانات المنتج في حالة التعديل
                if (_item != null)
                {
                    txtCode.Text = _item.Code;
                    txtName.Text = _item.Name;

                    // تحديد الوحدة من القائمة
                    foreach (ComboBoxItem item in cmbUnit.Items)
                    {
                        if (item.Content.ToString() == _item.Unit)
                        {
                            cmbUnit.SelectedItem = item;
                            break;
                        }
                    }

                    txtQuantity.Text = _item.Quantity.ToString();
                    txtPrice.Text = _item.Price.ToString();
                    txtDiscount.Text = _item.Discount.ToString();
                    txtTotal.Text = _item.Total.ToString();
                }
            }
            catch (Exception ex)
            {
                ex.ToInjazException("حدث خطأ أثناء تحميل بيانات المنتج").Handle(true);
            }
        }

        private void CalculateTotal()
        {
            try
            {
                // حساب الإجمالي
                if (double.TryParse(txtQuantity.Text, out double quantity) &&
                    double.TryParse(txtPrice.Text, out double price) &&
                    double.TryParse(txtDiscount.Text, out double discount))
                {
                    double total = (quantity * price) - discount;
                    txtTotal.Text = total.ToString("N2");
                }
            }
            catch (Exception ex)
            {
                ex.ToInjazException("حدث خطأ أثناء حساب الإجمالي").Handle(true);
            }
        }

        private void txtQuantity_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotal();
        }

        private void txtPrice_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotal();
        }

        private void txtDiscount_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotal();
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                txtCode.Text.ThrowIfNullOrWhiteSpace("رمز المنتج", "الرجاء إدخال رمز المنتج");
                txtName.Text.ThrowIfNullOrWhiteSpace("اسم المنتج", "الرجاء إدخال اسم المنتج");

                if (!double.TryParse(txtQuantity.Text, out double quantity) || quantity <= 0)
                {
                    throw new ValidationException("الرجاء إدخال كمية صحيحة", ErrorCodes.ValidationFailed);
                }

                if (!double.TryParse(txtPrice.Text, out double price) || price < 0)
                {
                    throw new ValidationException("الرجاء إدخال سعر صحيح", ErrorCodes.ValidationFailed);
                }

                if (!double.TryParse(txtDiscount.Text, out double discount) || discount < 0)
                {
                    throw new ValidationException("الرجاء إدخال خصم صحيح", ErrorCodes.ValidationFailed);
                }

                if (!double.TryParse(txtTotal.Text, out double total) || total < 0)
                {
                    throw new ValidationException("الإجمالي غير صحيح", ErrorCodes.ValidationFailed);
                }

                // تحديث بيانات المنتج
                _item.Code = txtCode.Text;
                _item.Name = txtName.Text;
                _item.Unit = (cmbUnit.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "قطعة";
                _item.Quantity = quantity;
                _item.Price = price;
                _item.Discount = discount;
                _item.Total = total;

                // إذا تم اختيار إضافة المنتج للمخزون
                if (chkAddToInventory.IsChecked == true)
                {
                    // في التطبيق الحقيقي، سيتم إضافة المنتج للمخزون هنا
                    // AddProductToInventory(_item);

                    // للتجربة فقط، نعرض رسالة
                    MessageBox.Show("تم إضافة المنتج للمخزون بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }

                // إغلاق النافذة بنجاح
                this.DialogResult = true;
            }
            catch (ValidationException ex)
            {
                ex.Handle(true);
            }
            catch (Exception ex)
            {
                ex.ToInjazException("حدث خطأ أثناء حفظ بيانات المنتج").Handle(true);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إلغاء العملية وإغلاق النافذة
            this.DialogResult = false;
        }
    }
}
