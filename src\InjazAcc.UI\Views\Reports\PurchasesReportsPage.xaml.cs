using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace InjazAcc.UI.Views.Reports
{
    /// <summary>
    /// Interaction logic for PurchasesReportsPage.xaml
    /// </summary>
    public partial class PurchasesReportsPage : Page
    {
        private ObservableCollection<PurchasesReportItem> _reportItems;

        public PurchasesReportsPage()
        {
            try
            {
                InitializeComponent();

                // تعيين التواريخ الافتراضية (الشهر الحالي)
                if (dpFromDate != null && dpToDate != null)
                {
                    dpFromDate.SelectedDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                    dpToDate.SelectedDate = DateTime.Now;
                }

                // تهيئة البيانات
                _reportItems = new ObservableCollection<PurchasesReportItem>();
                if (dgReport != null)
                {
                    dgReport.ItemsSource = _reportItems;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة صفحة تقارير المشتريات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void cmbReportType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // تغيير أعمدة الجدول حسب نوع التقرير
                switch (cmbReportType.SelectedIndex)
                {
                    case 0: // تقرير المشتريات اليومي
                    case 1: // تقرير المشتريات الشهري
                        ConfigureDailyPurchasesReportColumns();
                        break;
                    case 2: // تقرير المشتريات حسب المورد
                        ConfigureSupplierPurchasesReportColumns();
                        break;
                    case 3: // تقرير المشتريات حسب الصنف
                        ConfigureItemPurchasesReportColumns();
                        break;
                    case 4: // تقرير مرتجعات المشتريات
                        ConfigurePurchasesReturnsReportColumns();
                        break;
                    case 5: // تقرير تحليل المشتريات
                        ConfigurePurchasesAnalysisReportColumns();
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ConfigureDailyPurchasesReportColumns()
        {
            dgReport.Columns.Clear();

            dgReport.Columns.Add(new DataGridTextColumn { Header = "رقم الفاتورة", Binding = new System.Windows.Data.Binding("InvoiceNumber"), Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "التاريخ", Binding = new System.Windows.Data.Binding("Date") { StringFormat = "yyyy-MM-dd" }, Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "المورد", Binding = new System.Windows.Data.Binding("SupplierName"), Width = 150 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي الفاتورة", Binding = new System.Windows.Data.Binding("Total") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الخصم", Binding = new System.Windows.Data.Binding("Discount") { StringFormat = "N2" }, Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الضريبة", Binding = new System.Windows.Data.Binding("Tax") { StringFormat = "N2" }, Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الصافي", Binding = new System.Windows.Data.Binding("Net") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الحالة", Binding = new System.Windows.Data.Binding("Status"), Width = 100 });
        }

        private void ConfigureSupplierPurchasesReportColumns()
        {
            dgReport.Columns.Clear();

            dgReport.Columns.Add(new DataGridTextColumn { Header = "كود المورد", Binding = new System.Windows.Data.Binding("SupplierCode"), Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "اسم المورد", Binding = new System.Windows.Data.Binding("SupplierName"), Width = 150 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "عدد الفواتير", Binding = new System.Windows.Data.Binding("InvoiceCount"), Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي المشتريات", Binding = new System.Windows.Data.Binding("Total") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي الخصومات", Binding = new System.Windows.Data.Binding("Discount") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي الضرائب", Binding = new System.Windows.Data.Binding("Tax") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الصافي", Binding = new System.Windows.Data.Binding("Net") { StringFormat = "N2" }, Width = 120 });
        }

        private void ConfigureItemPurchasesReportColumns()
        {
            dgReport.Columns.Clear();

            dgReport.Columns.Add(new DataGridTextColumn { Header = "كود الصنف", Binding = new System.Windows.Data.Binding("ItemCode"), Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "اسم الصنف", Binding = new System.Windows.Data.Binding("ItemName"), Width = 150 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الكمية المشتراة", Binding = new System.Windows.Data.Binding("Quantity") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الوحدة", Binding = new System.Windows.Data.Binding("Unit"), Width = 80 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "متوسط سعر الشراء", Binding = new System.Windows.Data.Binding("AveragePrice") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي المشتريات", Binding = new System.Windows.Data.Binding("Total") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "نسبة من إجمالي المشتريات", Binding = new System.Windows.Data.Binding("Percentage") { StringFormat = "P2" }, Width = 150 });
        }

        private void ConfigurePurchasesReturnsReportColumns()
        {
            dgReport.Columns.Clear();

            dgReport.Columns.Add(new DataGridTextColumn { Header = "رقم المرتجع", Binding = new System.Windows.Data.Binding("ReturnNumber"), Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "التاريخ", Binding = new System.Windows.Data.Binding("Date") { StringFormat = "yyyy-MM-dd" }, Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "رقم الفاتورة الأصلية", Binding = new System.Windows.Data.Binding("OriginalInvoiceNumber"), Width = 150 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "المورد", Binding = new System.Windows.Data.Binding("SupplierName"), Width = 150 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي المرتجع", Binding = new System.Windows.Data.Binding("Total") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الخصم", Binding = new System.Windows.Data.Binding("Discount") { StringFormat = "N2" }, Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الضريبة", Binding = new System.Windows.Data.Binding("Tax") { StringFormat = "N2" }, Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "الصافي", Binding = new System.Windows.Data.Binding("Net") { StringFormat = "N2" }, Width = 120 });
        }

        private void ConfigurePurchasesAnalysisReportColumns()
        {
            dgReport.Columns.Clear();

            dgReport.Columns.Add(new DataGridTextColumn { Header = "الفترة", Binding = new System.Windows.Data.Binding("Period"), Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "عدد الفواتير", Binding = new System.Windows.Data.Binding("InvoiceCount"), Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "إجمالي المشتريات", Binding = new System.Windows.Data.Binding("Total") { StringFormat = "N2" }, Width = 120 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "متوسط قيمة الفاتورة", Binding = new System.Windows.Data.Binding("AverageInvoice") { StringFormat = "N2" }, Width = 150 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "نسبة النمو", Binding = new System.Windows.Data.Binding("GrowthRate") { StringFormat = "P2" }, Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "عدد الموردين", Binding = new System.Windows.Data.Binding("SupplierCount"), Width = 100 });
            dgReport.Columns.Add(new DataGridTextColumn { Header = "عدد الأصناف المشتراة", Binding = new System.Windows.Data.Binding("ItemCount"), Width = 150 });
        }

        private void btnShowReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحميل بيانات تجريبية
                LoadSampleData();

                // حساب الإجماليات
                CalculateTotals();

                MessageBox.Show("تم تحميل التقرير بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadSampleData()
        {
            _reportItems.Clear();

            // بيانات تجريبية حسب نوع التقرير
            switch (cmbReportType.SelectedIndex)
            {
                case 0: // تقرير المشتريات اليومي
                case 1: // تقرير المشتريات الشهري
                    LoadDailyPurchasesReportData();
                    break;
                case 2: // تقرير المشتريات حسب المورد
                    LoadSupplierPurchasesReportData();
                    break;
                case 3: // تقرير المشتريات حسب الصنف
                    LoadItemPurchasesReportData();
                    break;
                case 4: // تقرير مرتجعات المشتريات
                    LoadPurchasesReturnsReportData();
                    break;
                case 5: // تقرير تحليل المشتريات
                    LoadPurchasesAnalysisReportData();
                    break;
            }
        }

        private void LoadDailyPurchasesReportData()
        {
            // بيانات تجريبية لتقرير المشتريات اليومي
            _reportItems.Add(new PurchasesReportItem { InvoiceNumber = "PUR-001", Date = DateTime.Now.AddDays(-5), SupplierName = "شركة التوريدات العامة", Total = 8000, Discount = 400, Tax = 380, Net = 7980, Status = "مكتملة" });
            _reportItems.Add(new PurchasesReportItem { InvoiceNumber = "PUR-002", Date = DateTime.Now.AddDays(-4), SupplierName = "مؤسسة الإمداد", Total = 5500, Discount = 0, Tax = 275, Net = 5775, Status = "مكتملة" });
            _reportItems.Add(new PurchasesReportItem { InvoiceNumber = "PUR-003", Date = DateTime.Now.AddDays(-3), SupplierName = "شركة المواد الأولية", Total = 12000, Discount = 600, Tax = 570, Net = 11970, Status = "مكتملة" });
            _reportItems.Add(new PurchasesReportItem { InvoiceNumber = "PUR-004", Date = DateTime.Now.AddDays(-2), SupplierName = "مؤسسة التجهيزات", Total = 3500, Discount = 175, Tax = 166.25, Net = 3491.25, Status = "مكتملة" });
            _reportItems.Add(new PurchasesReportItem { InvoiceNumber = "PUR-005", Date = DateTime.Now.AddDays(-1), SupplierName = "شركة المعدات المكتبية", Total = 7000, Discount = 350, Tax = 332.5, Net = 6982.5, Status = "مكتملة" });
        }

        private void LoadSupplierPurchasesReportData()
        {
            // بيانات تجريبية لتقرير المشتريات حسب المورد
            _reportItems.Add(new PurchasesReportItem { SupplierCode = "S001", SupplierName = "شركة التوريدات العامة", InvoiceCount = 5, Total = 40000, Discount = 2000, Tax = 1900, Net = 39900 });
            _reportItems.Add(new PurchasesReportItem { SupplierCode = "S002", SupplierName = "مؤسسة الإمداد", InvoiceCount = 3, Total = 16500, Discount = 0, Tax = 825, Net = 17325 });
            _reportItems.Add(new PurchasesReportItem { SupplierCode = "S003", SupplierName = "شركة المواد الأولية", InvoiceCount = 4, Total = 48000, Discount = 2400, Tax = 2280, Net = 47880 });
            _reportItems.Add(new PurchasesReportItem { SupplierCode = "S004", SupplierName = "مؤسسة التجهيزات", InvoiceCount = 2, Total = 7000, Discount = 350, Tax = 332.5, Net = 6982.5 });
            _reportItems.Add(new PurchasesReportItem { SupplierCode = "S005", SupplierName = "شركة المعدات المكتبية", InvoiceCount = 3, Total = 21000, Discount = 1050, Tax = 997.5, Net = 20947.5 });
        }

        private void LoadItemPurchasesReportData()
        {
            // بيانات تجريبية لتقرير المشتريات حسب الصنف
            _reportItems.Add(new PurchasesReportItem { ItemCode = "I001", ItemName = "جهاز كمبيوتر محمول", Quantity = 15, Unit = "قطعة", AveragePrice = 3000, Total = 45000, Percentage = 0.35 });
            _reportItems.Add(new PurchasesReportItem { ItemCode = "I002", ItemName = "طابعة ليزر", Quantity = 8, Unit = "قطعة", AveragePrice = 1000, Total = 8000, Percentage = 0.06 });
            _reportItems.Add(new PurchasesReportItem { ItemCode = "I003", ItemName = "شاشة كمبيوتر", Quantity = 20, Unit = "قطعة", AveragePrice = 700, Total = 14000, Percentage = 0.11 });
            _reportItems.Add(new PurchasesReportItem { ItemCode = "I004", ItemName = "لوحة مفاتيح", Quantity = 30, Unit = "قطعة", AveragePrice = 100, Total = 3000, Percentage = 0.02 });
            _reportItems.Add(new PurchasesReportItem { ItemCode = "I005", ItemName = "ماوس لاسلكي", Quantity = 40, Unit = "قطعة", AveragePrice = 80, Total = 3200, Percentage = 0.025 });
        }

        private void LoadPurchasesReturnsReportData()
        {
            // بيانات تجريبية لتقرير مرتجعات المشتريات
            _reportItems.Add(new PurchasesReportItem { ReturnNumber = "RET-001", Date = DateTime.Now.AddDays(-4), OriginalInvoiceNumber = "PUR-001", SupplierName = "شركة التوريدات العامة", Total = 1500, Discount = 75, Tax = 71.25, Net = 1496.25 });
            _reportItems.Add(new PurchasesReportItem { ReturnNumber = "RET-002", Date = DateTime.Now.AddDays(-3), OriginalInvoiceNumber = "PUR-003", SupplierName = "شركة المواد الأولية", Total = 3000, Discount = 150, Tax = 142.5, Net = 2992.5 });
            _reportItems.Add(new PurchasesReportItem { ReturnNumber = "RET-003", Date = DateTime.Now.AddDays(-2), OriginalInvoiceNumber = "PUR-005", SupplierName = "شركة المعدات المكتبية", Total = 2000, Discount = 100, Tax = 95, Net = 1995 });
        }

        private void LoadPurchasesAnalysisReportData()
        {
            // بيانات تجريبية لتقرير تحليل المشتريات
            _reportItems.Add(new PurchasesReportItem { Period = "يناير 2023", InvoiceCount = 35, Total = 70000, AverageInvoice = 2000, GrowthRate = 0, SupplierCount = 10, ItemCount = 20 });
            _reportItems.Add(new PurchasesReportItem { Period = "فبراير 2023", InvoiceCount = 40, Total = 80000, AverageInvoice = 2000, GrowthRate = 0.14, SupplierCount = 12, ItemCount = 22 });
            _reportItems.Add(new PurchasesReportItem { Period = "مارس 2023", InvoiceCount = 45, Total = 90000, AverageInvoice = 2000, GrowthRate = 0.125, SupplierCount = 15, ItemCount = 25 });
            _reportItems.Add(new PurchasesReportItem { Period = "أبريل 2023", InvoiceCount = 50, Total = 100000, AverageInvoice = 2000, GrowthRate = 0.11, SupplierCount = 18, ItemCount = 28 });
            _reportItems.Add(new PurchasesReportItem { Period = "مايو 2023", InvoiceCount = 55, Total = 110000, AverageInvoice = 2000, GrowthRate = 0.1, SupplierCount = 20, ItemCount = 30 });
        }

        private void CalculateTotals()
        {
            double totalPurchases = 0;
            double totalDiscounts = 0;
            double totalTaxes = 0;
            double totalNet = 0;

            foreach (var item in _reportItems)
            {
                if (cmbReportType.SelectedIndex <= 2) // تقارير المشتريات اليومي والشهري وحسب المورد
                {
                    totalPurchases += item.Total;
                    totalDiscounts += item.Discount;
                    totalTaxes += item.Tax;
                    totalNet += item.Net;
                }
                else if (cmbReportType.SelectedIndex == 3) // تقرير المشتريات حسب الصنف
                {
                    totalPurchases += item.Total;
                }
                else if (cmbReportType.SelectedIndex == 4) // تقرير مرتجعات المشتريات
                {
                    totalPurchases += item.Total;
                    totalDiscounts += item.Discount;
                    totalTaxes += item.Tax;
                    totalNet += item.Net;
                }
            }

            txtTotalPurchases.Text = totalPurchases.ToString("N2");
            txtTotalDiscounts.Text = totalDiscounts.ToString("N2");
            txtTotalTaxes.Text = totalTaxes.ToString("N2");
            txtTotalNet.Text = totalNet.ToString("N2");
        }

        private void btnPrintReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري طباعة التقرير...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnExportExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري تصدير التقرير إلى Excel...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnExportPDF_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري تصدير التقرير إلى PDF...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnBackToReports_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // العودة إلى صفحة التقارير الرئيسية
                if (NavigationService != null)
                {
                    NavigationService.Navigate(new ReportsPage());
                }
                else
                {
                    MessageBox.Show("لا يمكن العودة إلى صفحة التقارير", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء العودة لصفحة التقارير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class PurchasesReportItem
    {
        // خصائص مشتركة
        public string InvoiceNumber { get; set; } = "";
        public DateTime Date { get; set; }
        public string SupplierName { get; set; } = "";
        public double Total { get; set; }
        public double Discount { get; set; }
        public double Tax { get; set; }
        public double Net { get; set; }
        public string Status { get; set; } = "";

        // خصائص تقرير المشتريات حسب المورد
        public string SupplierCode { get; set; } = "";
        public int InvoiceCount { get; set; }

        // خصائص تقرير المشتريات حسب الصنف
        public string ItemCode { get; set; } = "";
        public string ItemName { get; set; } = "";
        public double Quantity { get; set; }
        public string Unit { get; set; } = "";
        public double AveragePrice { get; set; }
        public double Percentage { get; set; }

        // خصائص تقرير مرتجعات المشتريات
        public string ReturnNumber { get; set; } = "";
        public string OriginalInvoiceNumber { get; set; } = "";

        // خصائص تقرير تحليل المشتريات
        public string Period { get; set; } = "";
        public double AverageInvoice { get; set; }
        public double GrowthRate { get; set; }
        public int SupplierCount { get; set; }
        public int ItemCount { get; set; }
    }
}
