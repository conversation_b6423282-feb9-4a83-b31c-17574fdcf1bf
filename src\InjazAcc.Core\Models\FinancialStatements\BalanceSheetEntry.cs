namespace InjazAcc.Core.Models.FinancialStatements
{
    /// <summary>
    /// نموذج بيانات عنصر في الميزانية العمومية
    /// </summary>
    public class BalanceSheetEntry
    {
        /// <summary>
        /// البيان
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// المبلغ
        /// </summary>
        public decimal Amount { get; set; }
        
        /// <summary>
        /// نوع العنصر (أصول، خصوم، حقوق ملكية)
        /// </summary>
        public BalanceSheetEntryType Type { get; set; }
        
        /// <summary>
        /// هل هو عنوان قسم
        /// </summary>
        public bool IsHeader { get; set; }
        
        /// <summary>
        /// هل هو إجمالي
        /// </summary>
        public bool IsTotal { get; set; }
        
        /// <summary>
        /// مستوى العنصر (للتنسيق)
        /// </summary>
        public int Level { get; set; }
        
        /// <summary>
        /// رقم الحساب (إذ<PERSON> كان مرتبط بحساب)
        /// </summary>
        public string AccountCode { get; set; }
    }
    
    /// <summary>
    /// أنواع عناصر الميزانية العمومية
    /// </summary>
    public enum BalanceSheetEntryType
    {
        CurrentAsset = 1,        // أصول متداولة
        FixedAsset = 2,          // أصول ثابتة
        OtherAsset = 3,          // أصول أخرى
        TotalAssets = 4,         // إجمالي الأصول
        CurrentLiability = 5,    // خصوم متداولة
        LongTermLiability = 6,   // خصوم طويلة الأجل
        TotalLiabilities = 7,    // إجمالي الخصوم
        Equity = 8,              // حقوق الملكية
        TotalEquity = 9,         // إجمالي حقوق الملكية
        TotalLiabilitiesEquity = 10 // إجمالي الخصوم وحقوق الملكية
    }
}
