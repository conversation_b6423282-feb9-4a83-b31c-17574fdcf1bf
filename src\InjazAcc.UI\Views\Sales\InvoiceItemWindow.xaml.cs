using System;
using System.Windows;
using System.Windows.Controls;

namespace InjazAcc.UI.Views.Sales
{
    /// <summary>
    /// Interaction logic for InvoiceItemWindow.xaml
    /// </summary>
    public partial class InvoiceItemWindow : Window
    {
        public InvoiceItem Item { get; private set; }
        private bool _isEditMode = false;

        public InvoiceItemWindow()
        {
            InitializeComponent();
            Item = new InvoiceItem();
            
            // تعيين القيم الافتراضية
            txtQuantity.Text = "1";
            txtDiscountPercentage.Text = "0";
            txtTaxPercentage.Text = "15";
        }

        public InvoiceItemWindow(InvoiceItem item)
        {
            InitializeComponent();
            Item = item;
            _isEditMode = true;
            
            // تعبئة بيانات الصنف
            txtTitle.Text = "تعديل صنف";
            cmbProduct.Text = item.Name;
            txtCode.Text = item.Code;
            cmbUnit.Text = item.Unit;
            txtAvailableQty.Text = GetAvailableQuantity(item.Code).ToString();
            txtQuantity.Text = item.Quantity.ToString();
            txtPrice.Text = item.Price.ToString("N2");
            txtDiscountPercentage.Text = item.DiscountPercentage.ToString("N2");
            txtDiscountAmount.Text = item.DiscountAmount.ToString("N2");
            txtTaxPercentage.Text = item.TaxPercentage.ToString("N2");
            txtTotal.Text = item.Total.ToString("N2");
        }

        private void cmbProduct_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbProduct.SelectedItem != null)
            {
                var productName = (cmbProduct.SelectedItem as ComboBoxItem).Content.ToString();
                
                // تعبئة بيانات الصنف المحدد (بيانات تجريبية)
                switch (productName)
                {
                    case "لوح خشب متوسط":
                        txtCode.Text = "P001";
                        cmbUnit.SelectedIndex = 0; // قطعة
                        txtAvailableQty.Text = "50";
                        txtPrice.Text = "150.00";
                        break;
                    case "مسامير 5 سم":
                        txtCode.Text = "P002";
                        cmbUnit.SelectedIndex = 2; // كيلو
                        txtAvailableQty.Text = "100";
                        txtPrice.Text = "75.50";
                        break;
                    case "دهان أبيض":
                        txtCode.Text = "P003";
                        cmbUnit.SelectedIndex = 3; // جالون
                        txtAvailableQty.Text = "30";
                        txtPrice.Text = "120.25";
                        break;
                    case "زجاج شفاف":
                        txtCode.Text = "P004";
                        cmbUnit.SelectedIndex = 1; // متر
                        txtAvailableQty.Text = "200";
                        txtPrice.Text = "85.75";
                        break;
                    case "أدوات سباكة":
                        txtCode.Text = "P005";
                        cmbUnit.SelectedIndex = 4; // علبة
                        txtAvailableQty.Text = "25";
                        txtPrice.Text = "220.00";
                        break;
                }
                
                CalculateTotals();
            }
        }

        private int GetAvailableQuantity(string productCode)
        {
            // بيانات تجريبية للكميات المتاحة
            switch (productCode)
            {
                case "P001": return 50;
                case "P002": return 100;
                case "P003": return 30;
                case "P004": return 200;
                case "P005": return 25;
                default: return 0;
            }
        }

        private void txtQuantity_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotals();
        }

        private void txtPrice_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotals();
        }

        private void txtDiscountPercentage_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotals();
        }

        private void txtTaxPercentage_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotals();
        }

        private void CalculateTotals()
        {
            try
            {
                // التحقق من صحة المدخلات
                if (string.IsNullOrEmpty(txtQuantity.Text) || string.IsNullOrEmpty(txtPrice.Text) ||
                    string.IsNullOrEmpty(txtDiscountPercentage.Text) || string.IsNullOrEmpty(txtTaxPercentage.Text))
                {
                    return;
                }
                
                // تحويل المدخلات إلى أرقام
                int quantity = int.Parse(txtQuantity.Text);
                decimal price = decimal.Parse(txtPrice.Text);
                decimal discountPercentage = decimal.Parse(txtDiscountPercentage.Text);
                decimal taxPercentage = decimal.Parse(txtTaxPercentage.Text);
                
                // حساب القيم
                decimal subtotal = quantity * price;
                decimal discountAmount = subtotal * (discountPercentage / 100);
                decimal afterDiscount = subtotal - discountAmount;
                decimal taxAmount = afterDiscount * (taxPercentage / 100);
                decimal total = afterDiscount + taxAmount;
                
                // عرض النتائج
                txtDiscountAmount.Text = discountAmount.ToString("N2");
                txtTotal.Text = total.ToString("N2");
            }
            catch (Exception)
            {
                // تجاهل أخطاء التحويل
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من صحة البيانات
            if (string.IsNullOrEmpty(cmbProduct.Text))
            {
                MessageBox.Show("الرجاء اختيار الصنف", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }
            
            if (string.IsNullOrEmpty(txtQuantity.Text) || !int.TryParse(txtQuantity.Text, out int quantity) || quantity <= 0)
            {
                MessageBox.Show("الرجاء إدخال كمية صحيحة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }
            
            if (string.IsNullOrEmpty(txtPrice.Text) || !decimal.TryParse(txtPrice.Text, out decimal price) || price <= 0)
            {
                MessageBox.Show("الرجاء إدخال سعر صحيح", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }
            
            // التحقق من توفر الكمية
            if (int.TryParse(txtAvailableQty.Text, out int availableQty) && quantity > availableQty)
            {
                MessageBox.Show("الكمية المطلوبة غير متوفرة في المخزون", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }
            
            // تعبئة بيانات الصنف
            Item.Code = txtCode.Text;
            Item.Name = cmbProduct.Text;
            Item.Unit = cmbUnit.Text;
            Item.Quantity = quantity;
            Item.Price = price;
            Item.DiscountPercentage = decimal.Parse(txtDiscountPercentage.Text);
            Item.DiscountAmount = decimal.Parse(txtDiscountAmount.Text);
            Item.TaxPercentage = decimal.Parse(txtTaxPercentage.Text);
            Item.TaxAmount = (price * quantity - Item.DiscountAmount) * (Item.TaxPercentage / 100);
            Item.Total = decimal.Parse(txtTotal.Text);
            
            // إغلاق النافذة
            this.DialogResult = true;
            this.Close();
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق النافذة
            this.DialogResult = false;
            this.Close();
        }
    }
}
