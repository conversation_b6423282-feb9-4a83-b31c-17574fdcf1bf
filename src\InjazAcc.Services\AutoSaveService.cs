using System;
using System.Threading;
using System.Threading.Tasks;
using InjazAcc.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace InjazAcc.Services
{
    /// <summary>
    /// خدمة الحفظ التلقائي للبيانات
    /// </summary>
    public class AutoSaveService : IAutoSaveService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<AutoSaveService> _logger;
        private readonly Timer _autoSaveTimer;
        private readonly object _lockObject = new object();
        private bool _disposed = false;
        private bool _isEnabled = true;

        // فترة الحفظ التلقائي (بالدقائق)
        private readonly int _autoSaveIntervalMinutes;

        public AutoSaveService(IUnitOfWork unitOfWork, ILogger<AutoSaveService> logger = null)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger;
            
            // قراءة فترة الحفظ التلقائي من الإعدادات (افتراضياً 5 دقائق)
            _autoSaveIntervalMinutes = GetAutoSaveInterval();
            
            // تهيئة مؤقت الحفظ التلقائي
            var interval = TimeSpan.FromMinutes(_autoSaveIntervalMinutes);
            _autoSaveTimer = new Timer(AutoSaveCallback, null, interval, interval);
            
            LogInfo($"تم تفعيل الحفظ التلقائي كل {_autoSaveIntervalMinutes} دقيقة");
        }

        /// <summary>
        /// تفعيل أو إلغاء تفعيل الحفظ التلقائي
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                _isEnabled = value;
                LogInfo($"الحفظ التلقائي: {(value ? "مفعل" : "معطل")}");
            }
        }

        /// <summary>
        /// حفظ البيانات يدوياً
        /// </summary>
        public async Task<bool> SaveNowAsync()
        {
            if (_disposed)
                return false;

            lock (_lockObject)
            {
                try
                {
                    LogInfo("بدء الحفظ اليدوي للبيانات...");
                    
                    // حفظ التغييرات
                    var result = _unitOfWork.SaveChangesAsync().Result;
                    
                    LogInfo($"تم حفظ البيانات بنجاح - عدد التغييرات: {result}");
                    return true;
                }
                catch (Exception ex)
                {
                    LogError($"خطأ أثناء الحفظ اليدوي: {ex.Message}", ex);
                    return false;
                }
            }
        }

        /// <summary>
        /// دالة الحفظ التلقائي
        /// </summary>
        private async void AutoSaveCallback(object state)
        {
            if (!_isEnabled || _disposed)
                return;

            lock (_lockObject)
            {
                try
                {
                    LogInfo("بدء الحفظ التلقائي للبيانات...");
                    
                    // حفظ التغييرات
                    var result = _unitOfWork.SaveChangesAsync().Result;
                    
                    if (result > 0)
                    {
                        LogInfo($"تم الحفظ التلقائي بنجاح - عدد التغييرات: {result}");
                    }
                    else
                    {
                        LogInfo("لا توجد تغييرات للحفظ");
                    }
                }
                catch (Exception ex)
                {
                    LogError($"خطأ أثناء الحفظ التلقائي: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// قراءة فترة الحفظ التلقائي من الإعدادات
        /// </summary>
        private int GetAutoSaveInterval()
        {
            try
            {
                // في التطبيق الحقيقي، سيتم قراءة هذه القيمة من قاعدة البيانات أو ملف الإعدادات
                // هنا نستخدم قيمة افتراضية
                return 5; // 5 دقائق
            }
            catch
            {
                return 5; // قيمة افتراضية في حالة الخطأ
            }
        }

        /// <summary>
        /// تسجيل رسالة معلومات
        /// </summary>
        private void LogInfo(string message)
        {
            _logger?.LogInformation($"[AutoSave] {message}");
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [AutoSave] {message}");
        }

        /// <summary>
        /// تسجيل رسالة خطأ
        /// </summary>
        private void LogError(string message, Exception ex = null)
        {
            _logger?.LogError(ex, $"[AutoSave] {message}");
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [AutoSave ERROR] {message}");
            if (ex != null)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [AutoSave ERROR] {ex.StackTrace}");
            }
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
                
                // إيقاف المؤقت
                _autoSaveTimer?.Dispose();
                
                // حفظ أخير قبل التنظيف
                try
                {
                    LogInfo("حفظ أخير قبل إغلاق خدمة الحفظ التلقائي...");
                    var result = _unitOfWork.SaveChangesAsync().Result;
                    LogInfo($"تم الحفظ الأخير - عدد التغييرات: {result}");
                }
                catch (Exception ex)
                {
                    LogError($"خطأ أثناء الحفظ الأخير: {ex.Message}", ex);
                }
                
                LogInfo("تم إيقاف خدمة الحفظ التلقائي");
            }
        }
    }
}
