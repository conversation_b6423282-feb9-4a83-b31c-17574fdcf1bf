<Page x:Class="InjazAcc.UI.Views.Suppliers.SuppliersPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Suppliers"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="900"
      Title="الموردين"
      FlowDirection="RightToLeft"
      FontFamily="Arial"
      TextElement.FontSize="14"
      TextElement.FontWeight="Regular"
      TextElement.Foreground="{DynamicResource MaterialDesignBody}"
      Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة -->
        <TextBlock Grid.Row="0" Text="إدارة الموردين" Style="{StaticResource PageTitle}" Margin="20,20,20,10"/>

        <!-- أدوات البحث والإضافة -->
        <Grid Grid.Row="1" Margin="20,0,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- البحث -->
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBox x:Name="txtSearch" Width="300" materialDesign:HintAssist.Hint="بحث عن مورد..." Margin="0,0,10,0"/>
                <ComboBox x:Name="cmbSearchBy" Width="150" SelectedIndex="0">
                    <ComboBoxItem Content="الاسم"/>
                    <ComboBoxItem Content="رقم الهاتف"/>
                    <ComboBoxItem Content="البريد الإلكتروني"/>
                    <ComboBoxItem Content="العنوان"/>
                </ComboBox>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="بحث" Margin="10,0,0,0" Click="btnSearch_Click"/>
            </StackPanel>

            <!-- إضافة مورد جديد -->
            <Button Grid.Column="1" Style="{StaticResource MaterialDesignRaisedButton}" Content="إضافة مورد جديد" Click="btnAddSupplier_Click"/>
        </Grid>

        <!-- قائمة الموردين -->
        <DataGrid Grid.Row="2" x:Name="dgSuppliers" AutoGenerateColumns="False" CanUserAddRows="False"
                  Style="{StaticResource DataGridStyle}" Margin="20,0,20,10" MouseDoubleClick="dgSuppliers_MouseDoubleClick">
            <DataGrid.Columns>
                <DataGridTextColumn Header="الرمز" Binding="{Binding Code}" Width="80"/>
                <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="*"/>
                <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="120"/>
                <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="150"/>
                <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance, StringFormat=N2}" Width="120"/>
                <DataGridTemplateColumn Width="120">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="تعديل" Click="btnEditSupplier_Click">
                                    <materialDesign:PackIcon Kind="Pencil" Width="18" Height="18"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="حذف" Click="btnDeleteSupplier_Click">
                                    <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="تفاصيل" Click="btnSupplierDetails_Click">
                                    <materialDesign:PackIcon Kind="Information" Width="18" Height="18"/>
                                </Button>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- شريط الإحصائيات -->
        <Grid Grid.Row="3" Margin="20,0,20,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Border Grid.Column="0" Style="{StaticResource StatCard}" Background="#4CAF50" Margin="0,0,5,0">
                <StackPanel>
                    <TextBlock Text="إجمالي الموردين" Style="{StaticResource StatTitle}"/>
                    <TextBlock x:Name="txtTotalSuppliers" Text="0" Style="{StaticResource StatValue}"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="1" Style="{StaticResource StatCard}" Background="#2196F3" Margin="5,0">
                <StackPanel>
                    <TextBlock Text="إجمالي المشتريات" Style="{StaticResource StatTitle}"/>
                    <TextBlock x:Name="txtTotalPurchases" Text="0.00 ر.س" Style="{StaticResource StatValue}"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="2" Style="{StaticResource StatCard}" Background="#FF9800" Margin="5,0,0,0">
                <StackPanel>
                    <TextBlock Text="إجمالي المستحقات" Style="{StaticResource StatTitle}"/>
                    <TextBlock x:Name="txtTotalDue" Text="0.00 ر.س" Style="{StaticResource StatValue}"/>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Page>
