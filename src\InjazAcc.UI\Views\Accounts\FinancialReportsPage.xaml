<Page x:Class="InjazAcc.UI.Views.Accounts.FinancialReportsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Accounts"
      mc:Ignorable="d" 
      d:DesignHeight="650" d:DesignWidth="900"
      Title="التقارير المالية"
      FlowDirection="RightToLeft"
      FontFamily="Arial"
      TextElement.FontSize="14"
      TextElement.FontWeight="Regular"
      TextElement.Foreground="{DynamicResource MaterialDesignBody}"
      Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- عنوان الصفحة -->
        <TextBlock Grid.Row="0" Text="التقارير المالية" Style="{StaticResource PageTitle}" Margin="20,20,20,10"/>
        
        <!-- بطاقات التقارير -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- تقرير ميزان المراجعة -->
            <Border Grid.Row="0" Grid.Column="0" Margin="10" Background="#4CAF50" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="TrialBalance_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="ميزان المراجعة" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="FileDocument" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="عرض ميزان المراجعة لفترة محددة" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- تقرير قائمة الدخل -->
            <Border Grid.Row="0" Grid.Column="1" Margin="10" Background="#2196F3" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="IncomeStatement_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="قائمة الدخل" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="ChartLine" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="عرض قائمة الدخل (الأرباح والخسائر) لفترة محددة" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- تقرير الميزانية العمومية -->
            <Border Grid.Row="1" Grid.Column="0" Margin="10" Background="#FF9800" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="BalanceSheet_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="الميزانية العمومية" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="ScaleBalance" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="عرض الميزانية العمومية في تاريخ محدد" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- تقرير التدفقات النقدية -->
            <Border Grid.Row="1" Grid.Column="1" Margin="10" Background="#9C27B0" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="CashFlow_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="التدفقات النقدية" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="CashMultiple" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="عرض تقرير التدفقات النقدية لفترة محددة" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- تقرير حركة الحسابات -->
            <Border Grid.Row="2" Grid.Column="0" Margin="10" Background="#607D8B" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="AccountMovements_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="حركة الحسابات" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="AccountDetails" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="عرض تقرير حركة الحسابات لفترة محددة" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- تقرير مقارنة الفترات -->
            <Border Grid.Row="2" Grid.Column="1" Margin="10" Background="#E91E63" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="ComparePeriods_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="مقارنة الفترات" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="ChartBar" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="مقارنة الأداء المالي بين فترتين مختلفتين" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Page>
