using System;
using System.Collections.Generic;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج القيد المحاسبي في النظام
    /// </summary>
    public class JournalEntry
    {
        public int Id { get; set; }
        public string ReferenceNumber { get; set; }
        public DateTime EntryDate { get; set; }
        public JournalEntryType Type { get; set; }
        public string Description { get; set; }
        public decimal TotalDebit { get; set; }
        public decimal TotalCredit { get; set; }
        public bool IsPosted { get; set; }
        public DateTime? PostedDate { get; set; }

        // العلاقة مع المستخدم الذي أنشأ القيد
        public int UserId { get; set; }
        public virtual User User { get; set; }

        // العلاقة مع الفاتورة (إن وجدت)
        public int? InvoiceId { get; set; }
        public virtual Invoice Invoice { get; set; }

        // العلاقة مع الدفعة (إن وجدت)
        public int? PaymentId { get; set; }
        public virtual Payment Payment { get; set; }

        // العلاقة مع عناصر القيد
        public virtual ICollection<JournalEntryItem> Items { get; set; }
    }

    // تم نقل تعريف أنواع القيود المحاسبية إلى ملف منفصل JournalEntryType.cs
}
