using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using InjazAcc.Core.Models;

namespace InjazAcc.UI.Views.Shared.PrintTemplates
{
    /// <summary>
    /// Interaction logic for InvoicePrintTemplate.xaml
    /// </summary>
    public partial class InvoicePrintTemplate : UserControl
    {
        public InvoicePrintTemplate()
        {
            InitializeComponent();
        }

        /// <summary>
        /// تهيئة قالب طباعة فاتورة المبيعات
        /// </summary>
        public void InitializeSalesInvoice(string invoiceNumber, DateTime invoiceDate, string customerName, 
            string customerPhone, string customerAddress, string paymentMethod, 
            IEnumerable<dynamic> items, double subtotal, double discount, double tax, double total, 
            double paidAmount, double remainingAmount, string notes = "")
        {
            try
            {
                // عنوان الفاتورة
                txtInvoiceTitle.Text = "فاتورة مبيعات";
                
                // معلومات الفاتورة
                txtInvoiceNumber.Text = "رقم الفاتورة: " + invoiceNumber;
                txtInvoiceDate.Text = "التاريخ: " + invoiceDate.ToString("dd/MM/yyyy");
                txtInvoiceTime.Text = "الوقت: " + invoiceDate.ToString("hh:mm tt");
                txtPaymentMethod.Text = "طريقة الدفع: " + paymentMethod;
                
                // معلومات العميل
                txtCustomerName.Text = "الاسم: " + customerName;
                txtCustomerPhone.Text = "الهاتف: " + customerPhone;
                txtCustomerAddress.Text = "العنوان: " + customerAddress;
                
                // تفاصيل المنتجات
                dgItems.ItemsSource = items;
                
                // ملخص الفاتورة
                txtSubtotal.Text = subtotal.ToString("N2") + " ر.س";
                txtTotalDiscount.Text = discount.ToString("N2") + " ر.س";
                txtTax.Text = tax.ToString("N2") + " ر.س";
                txtTotal.Text = total.ToString("N2") + " ر.س";
                txtPaidAmount.Text = paidAmount.ToString("N2") + " ر.س";
                txtRemainingAmount.Text = remainingAmount.ToString("N2") + " ر.س";
                
                // ملاحظات
                if (!string.IsNullOrEmpty(notes))
                {
                    txtNotes.Text = notes;
                }
                
                // تحميل شعار الشركة إذا كان متاحًا
                LoadCompanyLogo();
                
                // تحميل معلومات الشركة
                LoadCompanyInfo();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة قالب الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تهيئة قالب طباعة فاتورة المشتريات
        /// </summary>
        public void InitializePurchaseInvoice(string invoiceNumber, DateTime invoiceDate, string supplierName, 
            string supplierPhone, string supplierAddress, string paymentMethod, 
            IEnumerable<dynamic> items, double subtotal, double discount, double tax, double total, 
            double paidAmount, double remainingAmount, string notes = "")
        {
            try
            {
                // عنوان الفاتورة
                txtInvoiceTitle.Text = "فاتورة مشتريات";
                
                // معلومات الفاتورة
                txtInvoiceNumber.Text = "رقم الفاتورة: " + invoiceNumber;
                txtInvoiceDate.Text = "التاريخ: " + invoiceDate.ToString("dd/MM/yyyy");
                txtInvoiceTime.Text = "الوقت: " + invoiceDate.ToString("hh:mm tt");
                txtPaymentMethod.Text = "طريقة الدفع: " + paymentMethod;
                
                // معلومات المورد
                txtCustomerName.Text = "الاسم: " + supplierName;
                txtCustomerPhone.Text = "الهاتف: " + supplierPhone;
                txtCustomerAddress.Text = "العنوان: " + supplierAddress;
                
                // تفاصيل المنتجات
                dgItems.ItemsSource = items;
                
                // ملخص الفاتورة
                txtSubtotal.Text = subtotal.ToString("N2") + " ر.س";
                txtTotalDiscount.Text = discount.ToString("N2") + " ر.س";
                txtTax.Text = tax.ToString("N2") + " ر.س";
                txtTotal.Text = total.ToString("N2") + " ر.س";
                txtPaidAmount.Text = paidAmount.ToString("N2") + " ر.س";
                txtRemainingAmount.Text = remainingAmount.ToString("N2") + " ر.س";
                
                // ملاحظات
                if (!string.IsNullOrEmpty(notes))
                {
                    txtNotes.Text = notes;
                }
                
                // تحميل شعار الشركة إذا كان متاحًا
                LoadCompanyLogo();
                
                // تحميل معلومات الشركة
                LoadCompanyInfo();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة قالب الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل شعار الشركة
        /// </summary>
        private void LoadCompanyLogo()
        {
            try
            {
                // في التطبيق الحقيقي، سيتم تحميل الشعار من الإعدادات
                string logoPath = ""; // GetCompanyLogoPath();
                
                if (!string.IsNullOrEmpty(logoPath) && System.IO.File.Exists(logoPath))
                {
                    BitmapImage logo = new BitmapImage();
                    logo.BeginInit();
                    logo.UriSource = new Uri(logoPath, UriKind.Absolute);
                    logo.EndInit();
                    
                    imgCompanyLogo.Source = logo;
                }
                else
                {
                    // إخفاء الشعار إذا لم يكن متاحًا
                    imgCompanyLogo.Visibility = Visibility.Collapsed;
                }
            }
            catch
            {
                // إخفاء الشعار في حالة حدوث خطأ
                imgCompanyLogo.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// تحميل معلومات الشركة
        /// </summary>
        private void LoadCompanyInfo()
        {
            try
            {
                // في التطبيق الحقيقي، سيتم تحميل معلومات الشركة من الإعدادات
                CompanyInfo companyInfo = new CompanyInfo
                {
                    Name = "شركة نظام إنجاز المحاسبي",
                    Address = "الرياض، المملكة العربية السعودية",
                    Phone = "0555555555",
                    Email = "<EMAIL>",
                    TaxNumber = "*********"
                };
                
                txtCompanyName.Text = companyInfo.Name;
                txtCompanyAddress.Text = companyInfo.Address;
                txtCompanyPhone.Text = "هاتف: " + companyInfo.Phone;
                txtCompanyEmail.Text = "البريد الإلكتروني: " + companyInfo.Email;
                txtCompanyTaxNumber.Text = "الرقم الضريبي: " + companyInfo.TaxNumber;
            }
            catch
            {
                // استخدام القيم الافتراضية في حالة حدوث خطأ
            }
        }
    }
}
