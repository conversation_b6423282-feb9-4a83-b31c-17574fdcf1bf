using System;
using System.Collections.Generic;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج عملية التحويل المخزني
    /// </summary>
    public class InventoryTransfer
    {
        public int Id { get; set; }
        public string ReferenceNumber { get; set; }
        public DateTime TransferDate { get; set; }
        public string Notes { get; set; }
        
        // المخزن المصدر
        public int SourceWarehouseId { get; set; }
        public virtual Warehouse SourceWarehouse { get; set; }
        
        // المخزن الوجهة
        public int DestinationWarehouseId { get; set; }
        public virtual Warehouse DestinationWarehouse { get; set; }
        
        // المستخدم الذي قام بالعملية
        public int UserId { get; set; }
        public virtual User User { get; set; }
        
        // تفاصيل التحويل
        public virtual ICollection<InventoryTransferItem> TransferItems { get; set; }
    }
}
