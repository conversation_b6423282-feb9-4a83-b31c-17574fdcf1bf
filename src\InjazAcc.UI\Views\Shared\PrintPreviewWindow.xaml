<Window x:Class="InjazAcc.UI.Views.Shared.PrintPreviewWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Shared"
        mc:Ignorable="d"
        Title="معاينة الطباعة" Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Arial">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- شريط الأدوات -->
        <ToolBar Grid.Row="0" Margin="0,0,0,10">
            <Button x:Name="btnPrint" Click="btnPrint_Click" ToolTip="طباعة">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Printer" Width="20" Height="20" VerticalAlignment="Center"/>
                    <TextBlock Text="طباعة" Margin="5,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            
            <Separator/>
            
            <Button x:Name="btnZoomIn" Click="btnZoomIn_Click" ToolTip="تكبير">
                <materialDesign:PackIcon Kind="ZoomIn" Width="20" Height="20"/>
            </Button>
            
            <Button x:Name="btnZoomOut" Click="btnZoomOut_Click" ToolTip="تصغير">
                <materialDesign:PackIcon Kind="ZoomOut" Width="20" Height="20"/>
            </Button>
            
            <ComboBox x:Name="cmbZoom" Width="80" SelectionChanged="cmbZoom_SelectionChanged">
                <ComboBoxItem Content="50%" Tag="0.5"/>
                <ComboBoxItem Content="75%" Tag="0.75"/>
                <ComboBoxItem Content="100%" Tag="1.0" IsSelected="True"/>
                <ComboBoxItem Content="125%" Tag="1.25"/>
                <ComboBoxItem Content="150%" Tag="1.5"/>
                <ComboBoxItem Content="200%" Tag="2.0"/>
            </ComboBox>
            
            <Separator/>
            
            <Button x:Name="btnFirstPage" Click="btnFirstPage_Click" ToolTip="الصفحة الأولى">
                <materialDesign:PackIcon Kind="PageFirst" Width="20" Height="20"/>
            </Button>
            
            <Button x:Name="btnPreviousPage" Click="btnPreviousPage_Click" ToolTip="الصفحة السابقة">
                <materialDesign:PackIcon Kind="ChevronLeft" Width="20" Height="20"/>
            </Button>
            
            <TextBlock x:Name="txtPageInfo" Text="صفحة 1 من 1" VerticalAlignment="Center" Margin="10,0"/>
            
            <Button x:Name="btnNextPage" Click="btnNextPage_Click" ToolTip="الصفحة التالية">
                <materialDesign:PackIcon Kind="ChevronRight" Width="20" Height="20"/>
            </Button>
            
            <Button x:Name="btnLastPage" Click="btnLastPage_Click" ToolTip="الصفحة الأخيرة">
                <materialDesign:PackIcon Kind="PageLast" Width="20" Height="20"/>
            </Button>
        </ToolBar>
        
        <!-- محتوى المعاينة -->
        <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1">
            <ScrollViewer x:Name="scrollViewer" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto">
                <Grid x:Name="previewContainer" Background="LightGray">
                    <Border x:Name="pageBorder" Background="White" Width="794" Height="1123" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="20">
                        <ContentControl x:Name="previewContent"/>
                    </Border>
                </Grid>
            </ScrollViewer>
        </Border>
        
        <!-- شريط الحالة -->
        <StatusBar Grid.Row="2" Margin="0,10,0,0">
            <TextBlock x:Name="txtStatus" Text="جاهز للطباعة"/>
        </StatusBar>
    </Grid>
</Window>
