<Window x:Class="InjazAcc.UI.Views.Sales.InvoiceItemWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Sales"
        mc:Ignorable="d"
        Title="إضافة صنف" 
        Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        FontFamily="{materialDesign:MaterialDesignFont}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- عنوان النافذة -->
        <TextBlock Grid.Row="0" x:Name="txtTitle" Text="إضافة صنف جديد" Style="{StaticResource PageTitle}"/>
        
        <!-- نموذج إدخال بيانات الصنف -->
        <Grid Grid.Row="1" Margin="0,10,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- اختيار الصنف -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="الصنف:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <ComboBox Grid.Row="0" Grid.Column="1" x:Name="cmbProduct" Style="{StaticResource MaterialDesignOutlinedComboBox}" Margin="0,0,0,10" SelectionChanged="cmbProduct_SelectionChanged">
                <ComboBoxItem Content="لوح خشب متوسط"/>
                <ComboBoxItem Content="مسامير 5 سم"/>
                <ComboBoxItem Content="دهان أبيض"/>
                <ComboBoxItem Content="زجاج شفاف"/>
                <ComboBoxItem Content="أدوات سباكة"/>
            </ComboBox>
            <Button Grid.Row="0" Grid.Column="2" Style="{StaticResource MaterialDesignIconButton}" ToolTip="إضافة صنف جديد" Margin="5,0,0,10">
                <materialDesign:PackIcon Kind="Plus" Width="24" Height="24"/>
            </Button>
            
            <!-- كود الصنف -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="الكود:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <TextBox Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2" x:Name="txtCode" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False"/>
            
            <!-- وحدة القياس -->
            <TextBlock Grid.Row="2" Grid.Column="0" Text="الوحدة:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <ComboBox Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2" x:Name="cmbUnit" Style="{StaticResource MaterialDesignOutlinedComboBox}" Margin="0,0,0,10">
                <ComboBoxItem Content="قطعة"/>
                <ComboBoxItem Content="متر"/>
                <ComboBoxItem Content="كيلو"/>
                <ComboBoxItem Content="جالون"/>
                <ComboBoxItem Content="علبة"/>
            </ComboBox>
            
            <!-- الكمية المتاحة -->
            <TextBlock Grid.Row="3" Grid.Column="0" Text="الكمية المتاحة:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <TextBox Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="2" x:Name="txtAvailableQty" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False"/>
            
            <!-- الكمية -->
            <TextBlock Grid.Row="4" Grid.Column="0" Text="الكمية:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <TextBox Grid.Row="4" Grid.Column="1" Grid.ColumnSpan="2" x:Name="txtQuantity" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" TextChanged="txtQuantity_TextChanged"/>
            
            <!-- سعر الوحدة -->
            <TextBlock Grid.Row="5" Grid.Column="0" Text="سعر الوحدة:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <TextBox Grid.Row="5" Grid.Column="1" Grid.ColumnSpan="2" x:Name="txtPrice" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" TextChanged="txtPrice_TextChanged"/>
            
            <!-- نسبة الخصم -->
            <TextBlock Grid.Row="6" Grid.Column="0" Text="نسبة الخصم %:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <TextBox Grid.Row="6" Grid.Column="1" Grid.ColumnSpan="2" x:Name="txtDiscountPercentage" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" TextChanged="txtDiscountPercentage_TextChanged"/>
            
            <!-- قيمة الخصم -->
            <TextBlock Grid.Row="7" Grid.Column="0" Text="قيمة الخصم:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <TextBox Grid.Row="7" Grid.Column="1" Grid.ColumnSpan="2" x:Name="txtDiscountAmount" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False"/>
            
            <!-- نسبة الضريبة -->
            <TextBlock Grid.Row="8" Grid.Column="0" Text="نسبة الضريبة %:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <TextBox Grid.Row="8" Grid.Column="1" Grid.ColumnSpan="2" x:Name="txtTaxPercentage" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" TextChanged="txtTaxPercentage_TextChanged"/>
            
            <!-- الإجمالي -->
            <TextBlock Grid.Row="9" Grid.Column="0" Text="الإجمالي:" Style="{StaticResource FormLabel}" Margin="0,0,10,10" FontWeight="Bold"/>
            <TextBox Grid.Row="9" Grid.Column="1" Grid.ColumnSpan="2" x:Name="txtTotal" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False" FontWeight="Bold"/>
        </Grid>
        
        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="btnSave" Content="موافق" Style="{StaticResource ActionButton}" Click="btnSave_Click"/>
            <Button x:Name="btnCancel" Content="إلغاء" Style="{StaticResource MaterialDesignOutlinedButton}" Margin="5" Padding="15,5" MinWidth="100" Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
