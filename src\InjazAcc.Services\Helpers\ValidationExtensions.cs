using System;
using System.Collections.Generic;
using InjazAcc.Core.Exceptions;

namespace InjazAcc.Services.Helpers
{
    /// <summary>
    /// ملحقات لتسهيل التحقق من صحة البيانات
    /// </summary>
    public static class ValidationExtensions
    {
        /// <summary>
        /// التحقق من أن القيمة ليست فارغة (null)
        /// </summary>
        /// <typeparam name="T">نوع القيمة</typeparam>
        /// <param name="value">القيمة المراد التحقق منها</param>
        /// <param name="paramName">اسم المعلمة</param>
        /// <param name="errorMessage">رسالة الخطأ المخصصة (اختياري)</param>
        /// <returns>القيمة نفسها إذا كانت غير فارغة</returns>
        /// <exception cref="ValidationException">إذا كانت القيمة فارغة</exception>
        public static T ThrowIfNull<T>(this T value, string paramName, string errorMessage = null)
        {
            if (value == null)
            {
                var message = errorMessage ?? $"القيمة '{paramName}' لا يمكن أن تكون فارغة.";
                var validationException = new ValidationException(message, "ERR-VAL-002");
                validationException.AddValidationError(paramName, message);
                throw validationException;
            }

            return value;
        }

        /// <summary>
        /// التحقق من أن النص ليس فارغًا أو يحتوي على مسافات فقط
        /// </summary>
        /// <param name="value">النص المراد التحقق منه</param>
        /// <param name="paramName">اسم المعلمة</param>
        /// <param name="errorMessage">رسالة الخطأ المخصصة (اختياري)</param>
        /// <returns>النص نفسه إذا كان غير فارغ</returns>
        /// <exception cref="ValidationException">إذا كان النص فارغًا أو يحتوي على مسافات فقط</exception>
        public static string ThrowIfNullOrWhiteSpace(this string value, string paramName, string errorMessage = null)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                var message = errorMessage ?? $"النص '{paramName}' لا يمكن أن يكون فارغًا أو يحتوي على مسافات فقط.";
                var validationException = new ValidationException(message, "ERR-VAL-003");
                validationException.AddValidationError(paramName, message);
                throw validationException;
            }

            return value;
        }

        /// <summary>
        /// التحقق من أن القيمة تقع ضمن نطاق محدد
        /// </summary>
        /// <typeparam name="T">نوع القيمة</typeparam>
        /// <param name="value">القيمة المراد التحقق منها</param>
        /// <param name="min">الحد الأدنى</param>
        /// <param name="max">الحد الأقصى</param>
        /// <param name="paramName">اسم المعلمة</param>
        /// <param name="errorMessage">رسالة الخطأ المخصصة (اختياري)</param>
        /// <returns>القيمة نفسها إذا كانت ضمن النطاق</returns>
        /// <exception cref="ValidationException">إذا كانت القيمة خارج النطاق</exception>
        public static T ThrowIfOutOfRange<T>(this T value, T min, T max, string paramName, string errorMessage = null) where T : IComparable<T>
        {
            if (value.CompareTo(min) < 0 || value.CompareTo(max) > 0)
            {
                var message = errorMessage ?? $"القيمة '{paramName}' يجب أن تكون بين {min} و {max}.";
                var validationException = new ValidationException(message, "ERR-VAL-004");
                validationException.AddValidationError(paramName, message);
                throw validationException;
            }

            return value;
        }

        /// <summary>
        /// التحقق من صحة شرط معين
        /// </summary>
        /// <param name="condition">الشرط المراد التحقق منه</param>
        /// <param name="paramName">اسم المعلمة</param>
        /// <param name="errorMessage">رسالة الخطأ</param>
        /// <exception cref="ValidationException">إذا كان الشرط غير محقق</exception>
        public static void ThrowIfFalse(this bool condition, string paramName, string errorMessage)
        {
            if (!condition)
            {
                var validationException = new ValidationException(errorMessage, "ERR-VAL-005");
                validationException.AddValidationError(paramName, errorMessage);
                throw validationException;
            }
        }

        /// <summary>
        /// التحقق من أن المجموعة ليست فارغة
        /// </summary>
        /// <typeparam name="T">نوع عناصر المجموعة</typeparam>
        /// <param name="collection">المجموعة المراد التحقق منها</param>
        /// <param name="paramName">اسم المعلمة</param>
        /// <param name="errorMessage">رسالة الخطأ المخصصة (اختياري)</param>
        /// <returns>المجموعة نفسها إذا كانت غير فارغة</returns>
        /// <exception cref="ValidationException">إذا كانت المجموعة فارغة</exception>
        public static IEnumerable<T> ThrowIfEmpty<T>(this IEnumerable<T> collection, string paramName, string errorMessage = null)
        {
            if (collection == null || !collection.GetEnumerator().MoveNext())
            {
                var message = errorMessage ?? $"المجموعة '{paramName}' لا يمكن أن تكون فارغة.";
                var validationException = new ValidationException(message, "ERR-VAL-006");
                validationException.AddValidationError(paramName, message);
                throw validationException;
            }

            return collection;
        }
    }
}
