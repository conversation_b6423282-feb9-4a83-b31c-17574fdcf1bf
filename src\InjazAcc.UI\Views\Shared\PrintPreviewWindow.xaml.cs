using System;
using System.Printing;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using InjazAcc.Services.Printing;

namespace InjazAcc.UI.Views.Shared
{
    /// <summary>
    /// Interaction logic for PrintPreviewWindow.xaml
    /// </summary>
    public partial class PrintPreviewWindow : Window
    {
        private FrameworkElement _contentToPrint;
        private string _documentTitle;
        private double _zoomFactor = 1.0;
        private int _currentPage = 1;
        private int _totalPages = 1;

        public PrintPreviewWindow(FrameworkElement content, string title = "معاينة الطباعة")
        {
            InitializeComponent();
            
            _contentToPrint = content;
            _documentTitle = title;
            
            this.Title = title + " - معاينة الطباعة";
            
            // عرض المحتوى في المعاينة
            DisplayContent();
            
            // تحديث معلومات الصفحة
            UpdatePageInfo();
        }

        /// <summary>
        /// عرض المحتوى في المعاينة
        /// </summary>
        private void DisplayContent()
        {
            try
            {
                // نسخ المحتوى لتجنب تعديل العنصر الأصلي
                FrameworkElement contentCopy = _contentToPrint;
                
                // تحديد حجم المحتوى
                contentCopy.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                contentCopy.Arrange(new Rect(new Point(0, 0), contentCopy.DesiredSize));
                contentCopy.UpdateLayout();
                
                // عرض المحتوى في المعاينة
                previewContent.Content = contentCopy;
                
                // تطبيق التكبير/التصغير
                ApplyZoom();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض المحتوى: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تطبيق التكبير/التصغير
        /// </summary>
        private void ApplyZoom()
        {
            ScaleTransform scaleTransform = new ScaleTransform(_zoomFactor, _zoomFactor);
            previewContent.RenderTransform = scaleTransform;
            
            // تحديث حجم الصفحة
            pageBorder.Width = 794 * _zoomFactor; // A4 width in pixels at 96 DPI
            pageBorder.Height = 1123 * _zoomFactor; // A4 height in pixels at 96 DPI
        }

        /// <summary>
        /// تحديث معلومات الصفحة
        /// </summary>
        private void UpdatePageInfo()
        {
            txtPageInfo.Text = $"صفحة {_currentPage} من {_totalPages}";
        }

        /// <summary>
        /// حدث النقر على زر الطباعة
        /// </summary>
        private void btnPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // طباعة المحتوى
                PrintService.PrintInvoice(_contentToPrint, _documentTitle);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر التكبير
        /// </summary>
        private void btnZoomIn_Click(object sender, RoutedEventArgs e)
        {
            _zoomFactor += 0.1;
            if (_zoomFactor > 3.0) _zoomFactor = 3.0;
            
            ApplyZoom();
            
            // تحديث القائمة المنسدلة
            UpdateZoomComboBox();
        }

        /// <summary>
        /// حدث النقر على زر التصغير
        /// </summary>
        private void btnZoomOut_Click(object sender, RoutedEventArgs e)
        {
            _zoomFactor -= 0.1;
            if (_zoomFactor < 0.1) _zoomFactor = 0.1;
            
            ApplyZoom();
            
            // تحديث القائمة المنسدلة
            UpdateZoomComboBox();
        }

        /// <summary>
        /// تحديث القائمة المنسدلة للتكبير/التصغير
        /// </summary>
        private void UpdateZoomComboBox()
        {
            foreach (ComboBoxItem item in cmbZoom.Items)
            {
                double zoom = Convert.ToDouble(item.Tag);
                if (Math.Abs(_zoomFactor - zoom) < 0.05)
                {
                    cmbZoom.SelectedItem = item;
                    return;
                }
            }
            
            // إذا لم يتم العثور على قيمة مطابقة
            cmbZoom.SelectedIndex = -1;
        }

        /// <summary>
        /// حدث تغيير القائمة المنسدلة للتكبير/التصغير
        /// </summary>
        private void cmbZoom_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbZoom.SelectedItem != null)
            {
                ComboBoxItem selectedItem = cmbZoom.SelectedItem as ComboBoxItem;
                if (selectedItem != null && selectedItem.Tag != null)
                {
                    _zoomFactor = Convert.ToDouble(selectedItem.Tag);
                    ApplyZoom();
                }
            }
        }

        /// <summary>
        /// حدث النقر على زر الصفحة الأولى
        /// </summary>
        private void btnFirstPage_Click(object sender, RoutedEventArgs e)
        {
            _currentPage = 1;
            UpdatePageInfo();
        }

        /// <summary>
        /// حدث النقر على زر الصفحة السابقة
        /// </summary>
        private void btnPreviousPage_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                UpdatePageInfo();
            }
        }

        /// <summary>
        /// حدث النقر على زر الصفحة التالية
        /// </summary>
        private void btnNextPage_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage < _totalPages)
            {
                _currentPage++;
                UpdatePageInfo();
            }
        }

        /// <summary>
        /// حدث النقر على زر الصفحة الأخيرة
        /// </summary>
        private void btnLastPage_Click(object sender, RoutedEventArgs e)
        {
            _currentPage = _totalPages;
            UpdatePageInfo();
        }
    }
}
