<Page x:Class="InjazAcc.UI.Views.Sales.SalesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Sales"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="1100"
      Title="صفحة المبيعات">

    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة -->
        <TextBlock Grid.Row="0" Text="إدارة المبيعات" Style="{StaticResource PageTitle}"/>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,0,15">
            <Button x:Name="btnNewSale" Click="btnNewSale_Click" Style="{StaticResource ActionButton}" Margin="4,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="CartPlus" Width="18" Height="18" VerticalAlignment="Center"/>
                    <TextBlock Text="فاتورة مبيعات جديدة" Margin="6,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>

            <Button x:Name="btnNewReturn" Click="btnNewReturn_Click" Style="{StaticResource ActionButton}" Margin="4,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="CartRemove" Width="18" Height="18" VerticalAlignment="Center"/>
                    <TextBlock Text="مردودات مبيعات" Margin="6,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </StackPanel>

        <!-- محتوى الصفحة -->
        <TabControl Grid.Row="2" Style="{StaticResource MaterialDesignTabControl}" FlowDirection="RightToLeft">
            <!-- قائمة الفواتير -->
            <TabItem Header="فواتير المبيعات">
                <Grid Margin="8">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- أدوات البحث والفلترة -->
                    <Grid Grid.Row="0" Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0" materialDesign:HintAssist.Hint="بحث..." Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,8,0"/>

                        <ComboBox Grid.Column="1" materialDesign:HintAssist.Hint="الحالة" Style="{StaticResource MaterialDesignOutlinedComboBox}" MinWidth="110" Margin="0,0,8,0">
                            <ComboBoxItem Content="الكل"/>
                            <ComboBoxItem Content="مؤكدة"/>
                            <ComboBoxItem Content="مدفوعة"/>
                            <ComboBoxItem Content="مدفوعة جزئياً"/>
                            <ComboBoxItem Content="ملغاة"/>
                        </ComboBox>

                        <DatePicker Grid.Column="2" materialDesign:HintAssist.Hint="من تاريخ" Style="{StaticResource MaterialDesignOutlinedDatePicker}" Width="110" Margin="0,0,8,0"/>

                        <DatePicker Grid.Column="3" materialDesign:HintAssist.Hint="إلى تاريخ" Style="{StaticResource MaterialDesignOutlinedDatePicker}" Width="110"/>
                    </Grid>

                    <!-- جدول الفواتير -->
                    <DataGrid Grid.Row="1" x:Name="dgSales" Style="{StaticResource DataGridStyle}" AutoGenerateColumns="False" IsReadOnly="True" MouseDoubleClick="dgSales_MouseDoubleClick">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="90"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding InvoiceDate, StringFormat=yyyy-MM-dd}" Width="90"/>
                            <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}" Width="180"/>
                            <DataGridTextColumn Header="المبلغ الإجمالي" Binding="{Binding TotalAmount, StringFormat=N2}" Width="100"/>
                            <DataGridTextColumn Header="المبلغ المدفوع" Binding="{Binding PaidAmount, StringFormat=N2}" Width="100"/>
                            <DataGridTextColumn Header="المبلغ المتبقي" Binding="{Binding RemainingAmount, StringFormat=N2}" Width="100"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="عرض" Click="btnViewInvoice_Click" Width="28" Height="28" Padding="2">
                                                <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="تعديل" Click="btnEditInvoice_Click" Width="28" Height="28" Padding="2">
                                                <materialDesign:PackIcon Kind="Pencil" Width="16" Height="16"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="طباعة" Click="btnPrintInvoice_Click" Width="28" Height="28" Padding="2">
                                                <materialDesign:PackIcon Kind="Printer" Width="16" Height="16"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="حذف" Click="btnDeleteInvoice_Click" Width="28" Height="28" Padding="2">
                                                <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- قائمة المردودات -->
            <TabItem Header="مردودات المبيعات">
                <Grid Margin="8">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- أدوات البحث والفلترة -->
                    <Grid Grid.Row="0" Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0" materialDesign:HintAssist.Hint="بحث..." Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,8,0"/>

                        <DatePicker Grid.Column="1" materialDesign:HintAssist.Hint="من تاريخ" Style="{StaticResource MaterialDesignOutlinedDatePicker}" Width="110" Margin="0,0,8,0"/>

                        <DatePicker Grid.Column="2" materialDesign:HintAssist.Hint="إلى تاريخ" Style="{StaticResource MaterialDesignOutlinedDatePicker}" Width="110"/>
                    </Grid>

                    <!-- جدول المردودات -->
                    <DataGrid Grid.Row="1" x:Name="dgReturns" Style="{StaticResource DataGridStyle}" AutoGenerateColumns="False" IsReadOnly="True" MouseDoubleClick="dgReturns_MouseDoubleClick">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم المردود" Binding="{Binding InvoiceNumber}" Width="90"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding InvoiceDate, StringFormat=yyyy-MM-dd}" Width="90"/>
                            <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}" Width="180"/>
                            <DataGridTextColumn Header="رقم الفاتورة الأصلية" Binding="{Binding OriginalInvoiceNumber}" Width="120"/>
                            <DataGridTextColumn Header="المبلغ الإجمالي" Binding="{Binding TotalAmount, StringFormat=N2}" Width="100"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="عرض" Click="btnViewReturn_Click" Width="28" Height="28" Padding="2">
                                                <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="طباعة" Click="btnPrintReturn_Click" Width="28" Height="28" Padding="2">
                                                <materialDesign:PackIcon Kind="Printer" Width="16" Height="16"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- إحصائيات المبيعات -->
            <TabItem Header="إحصائيات المبيعات">
                <Grid Margin="8">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- فلاتر الإحصائيات -->
                    <Grid Grid.Row="0" Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <ComboBox Grid.Column="0" materialDesign:HintAssist.Hint="الفترة" Style="{StaticResource MaterialDesignOutlinedComboBox}" MinWidth="110" Margin="0,0,8,0">
                            <ComboBoxItem Content="اليوم"/>
                            <ComboBoxItem Content="الأسبوع الحالي"/>
                            <ComboBoxItem Content="الشهر الحالي"/>
                            <ComboBoxItem Content="الربع الحالي"/>
                            <ComboBoxItem Content="السنة الحالية"/>
                            <ComboBoxItem Content="فترة محددة"/>
                        </ComboBox>

                        <DatePicker Grid.Column="1" materialDesign:HintAssist.Hint="من تاريخ" Style="{StaticResource MaterialDesignOutlinedDatePicker}" Width="110" Margin="0,0,8,0"/>

                        <DatePicker Grid.Column="2" materialDesign:HintAssist.Hint="إلى تاريخ" Style="{StaticResource MaterialDesignOutlinedDatePicker}" Width="110" Margin="0,0,8,0"/>

                        <Button Grid.Column="4" Content="تحديث" Style="{StaticResource ActionButton}"/>
                    </Grid>

                    <!-- بطاقات الإحصائيات -->
                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- بطاقات الإحصائيات -->
                        <Grid Grid.Row="0" Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- إجمالي المبيعات -->
                            <materialDesign:Card Grid.Column="0" Margin="5" Padding="15" Background="{DynamicResource PrimaryHueLightBrush}" UniformCornerRadius="8">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <materialDesign:PackIcon Grid.Row="0" Kind="CartOutline" Width="32" Height="32" HorizontalAlignment="Right" Foreground="White"/>
                                    <TextBlock Grid.Row="1" Text="إجمالي المبيعات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,10,0,5" Foreground="White"/>
                                    <TextBlock Grid.Row="2" Text="25,000 ر.س" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right" Foreground="White"/>
                                </Grid>
                            </materialDesign:Card>

                            <!-- عدد الفواتير -->
                            <materialDesign:Card Grid.Column="1" Margin="5" Padding="15" Background="#4CAF50" UniformCornerRadius="8">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <materialDesign:PackIcon Grid.Row="0" Kind="FileDocumentOutline" Width="32" Height="32" HorizontalAlignment="Right" Foreground="White"/>
                                    <TextBlock Grid.Row="1" Text="عدد الفواتير" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,10,0,5" Foreground="White"/>
                                    <TextBlock Grid.Row="2" Text="45" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right" Foreground="White"/>
                                </Grid>
                            </materialDesign:Card>

                            <!-- إجمالي المردودات -->
                            <materialDesign:Card Grid.Column="2" Margin="5" Padding="15" Background="#FF5722" UniformCornerRadius="8">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <materialDesign:PackIcon Grid.Row="0" Kind="CartRemove" Width="32" Height="32" HorizontalAlignment="Right" Foreground="White"/>
                                    <TextBlock Grid.Row="1" Text="إجمالي المردودات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,10,0,5" Foreground="White"/>
                                    <TextBlock Grid.Row="2" Text="1,500 ر.س" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right" Foreground="White"/>
                                </Grid>
                            </materialDesign:Card>

                            <!-- صافي المبيعات -->
                            <materialDesign:Card Grid.Column="3" Margin="5" Padding="15" Background="#2196F3" UniformCornerRadius="8">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <materialDesign:PackIcon Grid.Row="0" Kind="CashRegister" Width="32" Height="32" HorizontalAlignment="Right" Foreground="White"/>
                                    <TextBlock Grid.Row="1" Text="صافي المبيعات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,10,0,5" Foreground="White"/>
                                    <TextBlock Grid.Row="2" Text="23,500 ر.س" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right" Foreground="White"/>
                                </Grid>
                            </materialDesign:Card>
                        </Grid>

                        <!-- رسم بياني للمبيعات -->
                        <materialDesign:Card Grid.Row="1" Margin="5" Padding="15" UniformCornerRadius="8">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="مبيعات الفترة" Style="{StaticResource SectionTitle}"/>

                                <!-- هنا يمكن إضافة رسم بياني للمبيعات -->
                                <Border Grid.Row="1" BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="1" Margin="0,10,0,0">
                                    <TextBlock Text="الرسم البياني للمبيعات" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18" Foreground="Gray"/>
                                </Border>
                            </Grid>
                        </materialDesign:Card>
                    </Grid>

                    <!-- أزرار التصدير -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,20,0,0">
                        <Button Style="{StaticResource ActionButton}" Margin="5,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileExcel" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="تصدير إلى Excel" Margin="8,0,0,0" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource ActionButton}" Margin="5,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FilePdf" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="تصدير إلى PDF" Margin="8,0,0,0" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</Page>
