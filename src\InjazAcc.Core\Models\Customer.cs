using System;
using System.Collections.Generic;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج العميل في النظام
    /// </summary>
    public class Customer
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string ContactPerson { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string TaxNumber { get; set; }
        public decimal CreditLimit { get; set; }
        public decimal OpeningBalance { get; set; }
        public DateTime OpeningBalanceDate { get; set; }
        public bool IsActive { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        
        // العلاقة مع الفواتير
        public virtual ICollection<Invoice> Invoices { get; set; }
        
        // العلاقة مع المدفوعات
        public virtual ICollection<Payment> Payments { get; set; }
    }
}
