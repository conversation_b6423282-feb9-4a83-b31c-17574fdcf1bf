﻿#pragma checksum "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "610376899929A44DF597DE9D0752BC26518D1FDE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using InjazAcc.UI.Views.Reports;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InjazAcc.UI.Views.Reports {
    
    
    /// <summary>
    /// SalesReportsPage
    /// </summary>
    public partial class SalesReportsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 58 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbReportType;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpFromDate;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpToDate;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgReport;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalSales;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalDiscounts;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalTaxes;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalNet;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InjazAcc.UI;component/views/reports/salesreportspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 33 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnBackToReports_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.cmbReportType = ((System.Windows.Controls.ComboBox)(target));
            
            #line 58 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
            this.cmbReportType.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbReportType_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.dpFromDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 4:
            this.dpToDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 5:
            
            #line 75 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnShowReport_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.dgReport = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 7:
            
            #line 102 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnPrintReport_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 103 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnExportExcel_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 104 "..\..\..\..\..\Views\Reports\SalesReportsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnExportPDF_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.txtTotalSales = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.txtTotalDiscounts = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.txtTotalTaxes = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.txtTotalNet = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

