<Window x:Class="InjazAcc.UI.Views.Sales.ReturnItemWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Sales"
        mc:Ignorable="d"
        Title="إضافة صنف للمردود" 
        Height="450" Width="600"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        FontFamily="{materialDesign:MaterialDesignFont}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- عنوان النافذة -->
        <TextBlock Grid.Row="0" x:Name="txtTitle" Text="إضافة صنف للمردود" Style="{StaticResource PageTitle}"/>
        
        <!-- نموذج إدخال بيانات الصنف -->
        <Grid Grid.Row="1" Margin="0,10,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- اختيار الصنف -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="الصنف:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <ComboBox Grid.Row="0" Grid.Column="1" x:Name="cmbProduct" Style="{StaticResource MaterialDesignOutlinedComboBox}" Margin="0,0,0,10" SelectionChanged="cmbProduct_SelectionChanged"/>
            
            <!-- كود الصنف -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="الكود:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtCode" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False"/>
            
            <!-- وحدة القياس -->
            <TextBlock Grid.Row="2" Grid.Column="0" Text="الوحدة:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtUnit" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False"/>
            
            <!-- الكمية المباعة -->
            <TextBlock Grid.Row="3" Grid.Column="0" Text="الكمية المباعة:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <TextBox Grid.Row="3" Grid.Column="1" x:Name="txtSoldQuantity" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False"/>
            
            <!-- كمية المردود -->
            <TextBlock Grid.Row="4" Grid.Column="0" Text="كمية المردود:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <TextBox Grid.Row="4" Grid.Column="1" x:Name="txtReturnQuantity" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" TextChanged="txtReturnQuantity_TextChanged"/>
            
            <!-- سعر الوحدة -->
            <TextBlock Grid.Row="5" Grid.Column="0" Text="سعر الوحدة:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
            <TextBox Grid.Row="5" Grid.Column="1" x:Name="txtPrice" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False"/>
            
            <!-- الإجمالي -->
            <TextBlock Grid.Row="6" Grid.Column="0" Text="الإجمالي:" Style="{StaticResource FormLabel}" Margin="0,0,10,10" FontWeight="Bold"/>
            <TextBox Grid.Row="6" Grid.Column="1" x:Name="txtTotal" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False" FontWeight="Bold"/>
        </Grid>
        
        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="btnSave" Content="موافق" Style="{StaticResource ActionButton}" Click="btnSave_Click"/>
            <Button x:Name="btnCancel" Content="إلغاء" Style="{StaticResource MaterialDesignOutlinedButton}" Margin="5" Padding="15,5" MinWidth="100" Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
