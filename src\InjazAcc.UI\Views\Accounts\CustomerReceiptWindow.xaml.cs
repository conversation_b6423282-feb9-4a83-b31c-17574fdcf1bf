using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;

namespace InjazAcc.UI.Views.Accounts
{
    /// <summary>
    /// Interaction logic for CustomerReceiptWindow.xaml
    /// </summary>
    public partial class CustomerReceiptWindow : Window
    {
        private Dictionary<string, double> _customerBalances;
        
        public DateTime TransactionDate { get; private set; }
        public string CustomerName { get; private set; }
        public double Amount { get; private set; }
        public string PaymentMethod { get; private set; }
        public string Description { get; private set; }
        public string Attachment { get; private set; }

        public CustomerReceiptWindow()
        {
            InitializeComponent();
            dpDate.SelectedDate = DateTime.Now;
            cmbCustomer.SelectedIndex = 0;
            cmbPaymentMethod.SelectedIndex = 0;
            
            // بيانات تجريبية لأرصدة العملاء
            _customerBalances = new Dictionary<string, double>
            {
                { "شركة الأمل للتجارة", 15000 },
                { "مؤسسة النور", 8500 },
                { "شركة الصفا للمقاولات", 25000 },
                { "مؤسسة الإبداع", 12000 },
                { "شركة المستقبل", 18000 }
            };
            
            UpdateCustomerBalance();
        }

        private void UpdateCustomerBalance()
        {
            try
            {
                string customerName = (cmbCustomer.SelectedItem as ComboBoxItem)?.Content.ToString();
                if (!string.IsNullOrEmpty(customerName) && _customerBalances.ContainsKey(customerName))
                {
                    txtCustomerBalance.Text = _customerBalances[customerName].ToString("N2") + " ر.س";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void cmbCustomer_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateCustomerBalance();
        }

        private void btnBrowse_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|ملفات الصور (*.jpg, *.jpeg, *.png)|*.jpg;*.jpeg;*.png";
                
                if (openFileDialog.ShowDialog() == true)
                {
                    txtAttachment.Text = openFileDialog.FileName;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!dpDate.SelectedDate.HasValue)
                {
                    MessageBox.Show("الرجاء تحديد التاريخ", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    dpDate.Focus();
                    return;
                }

                if (cmbCustomer.SelectedItem == null)
                {
                    MessageBox.Show("الرجاء اختيار العميل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    cmbCustomer.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtAmount.Text))
                {
                    MessageBox.Show("الرجاء إدخال المبلغ", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtAmount.Focus();
                    return;
                }

                if (!double.TryParse(txtAmount.Text, out double amount) || amount <= 0)
                {
                    MessageBox.Show("الرجاء إدخال مبلغ صحيح أكبر من صفر", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtAmount.Focus();
                    return;
                }

                string customerName = (cmbCustomer.SelectedItem as ComboBoxItem)?.Content.ToString();
                if (_customerBalances.ContainsKey(customerName) && amount > _customerBalances[customerName])
                {
                    MessageBox.Show("المبلغ المدخل أكبر من رصيد العميل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtAmount.Focus();
                    return;
                }

                if (cmbPaymentMethod.SelectedItem == null)
                {
                    MessageBox.Show("الرجاء اختيار طريقة التحصيل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    cmbPaymentMethod.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtDescription.Text))
                {
                    MessageBox.Show("الرجاء إدخال البيان", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtDescription.Focus();
                    return;
                }

                // حفظ البيانات
                TransactionDate = dpDate.SelectedDate.Value;
                CustomerName = customerName;
                Amount = amount;
                PaymentMethod = (cmbPaymentMethod.SelectedItem as ComboBoxItem)?.Content.ToString();
                Description = txtDescription.Text;
                Attachment = txtAttachment.Text;

                // تحديث رصيد العميل
                if (_customerBalances.ContainsKey(customerName))
                {
                    _customerBalances[customerName] -= amount;
                }

                // إغلاق النافذة بنجاح
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إلغاء العملية وإغلاق النافذة
            this.DialogResult = false;
        }
    }
}
