using System;
using System.Diagnostics;
using System.IO;
using System.Text;
using InjazAcc.Core.Exceptions;
using InjazAcc.Core.Interfaces;

namespace InjazAcc.Services.Helpers
{
    /// <summary>
    /// خدمة مركزية لمعالجة الاستثناءات في التطبيق
    /// </summary>
    public class ExceptionHandlingService : IExceptionHandler
    {
        private static readonly string LogDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
        private static readonly object LogLock = new object();

        /// <summary>
        /// معالجة الاستثناء وعرض رسالة مناسبة للمستخدم
        /// </summary>
        /// <param name="exception">الاستثناء المراد معالجته</param>
        /// <param name="showMessageToUser">ما إذا كان يجب عرض رسالة للمستخدم</param>
        /// <returns>رسالة الخطأ المعروضة للمستخدم</returns>
        public string HandleException(Exception exception, bool showMessageToUser = true)
        {
            string userMessage = GetUserFriendlyMessage(exception);

            // تسجيل الاستثناء
            LogException(exception);

            // عرض رسالة للمستخدم إذا كان مطلوبًا
            if (showMessageToUser)
            {
                ShowErrorMessage(userMessage, GetMessageTitle(exception), GetMessageIcon(exception));
            }

            return userMessage;
        }

        /// <summary>
        /// معالجة الاستثناء بشكل ثابت (static) وعرض رسالة مناسبة للمستخدم
        /// </summary>
        /// <param name="exception">الاستثناء المراد معالجته</param>
        /// <param name="showMessageToUser">ما إذا كان يجب عرض رسالة للمستخدم</param>
        /// <returns>رسالة الخطأ المعروضة للمستخدم</returns>
        public static string HandleExceptionStatic(Exception exception, bool showMessageToUser = true)
        {
            return new ExceptionHandlingService().HandleException(exception, showMessageToUser);
        }

        /// <summary>
        /// الحصول على رسالة خطأ مناسبة للمستخدم
        /// </summary>
        /// <param name="exception">الاستثناء</param>
        /// <returns>رسالة الخطأ</returns>
        private static string GetUserFriendlyMessage(Exception exception)
        {
            // التعامل مع الاستثناءات المخصصة
            if (exception is InjazAccException injazException)
            {
                return injazException.GetDetailedMessage();
            }

            // التعامل مع أنواع الاستثناءات المعروفة
            if (exception is ArgumentNullException)
            {
                return "تم تمرير قيمة فارغة إلى دالة لا تقبل قيم فارغة.";
            }

            if (exception is ArgumentException)
            {
                return "تم تمرير قيمة غير صالحة: " + exception.Message;
            }

            if (exception is InvalidOperationException)
            {
                return "تم محاولة تنفيذ عملية غير صالحة: " + exception.Message;
            }

            if (exception is IOException)
            {
                return "حدث خطأ أثناء قراءة أو كتابة الملفات: " + exception.Message;
            }

            if (exception is UnauthorizedAccessException)
            {
                return "ليس لديك صلاحية للوصول إلى هذا المورد.";
            }

            // رسالة افتراضية للاستثناءات غير المعروفة
            return "حدث خطأ غير متوقع في التطبيق. الرجاء المحاولة مرة أخرى.";
        }

        /// <summary>
        /// الحصول على عنوان رسالة الخطأ
        /// </summary>
        /// <param name="exception">الاستثناء</param>
        /// <returns>عنوان الرسالة</returns>
        private static string GetMessageTitle(Exception exception)
        {
            if (exception is ValidationException)
            {
                return "خطأ في البيانات";
            }

            if (exception is AuthenticationException)
            {
                return "خطأ في المصادقة";
            }

            if (exception is AuthorizationException)
            {
                return "خطأ في الصلاحيات";
            }

            if (exception is DatabaseException)
            {
                return "خطأ في قاعدة البيانات";
            }

            if (exception is BusinessException)
            {
                return "خطأ في العملية";
            }

            return "خطأ";
        }

        /// <summary>
        /// الحصول على أيقونة رسالة الخطأ
        /// </summary>
        /// <param name="exception">الاستثناء</param>
        /// <returns>أيقونة الرسالة</returns>
        private static string GetMessageIcon(Exception exception)
        {
            if (exception is InjazAccException injazException)
            {
                switch (injazException.Severity)
                {
                    case ErrorSeverity.Information:
                        return "Information";
                    case ErrorSeverity.Warning:
                        return "Warning";
                    case ErrorSeverity.Critical:
                        return "Stop";
                    default:
                        return "Error";
                }
            }

            return "Error";
        }

        /// <summary>
        /// عرض رسالة خطأ للمستخدم
        /// </summary>
        /// <param name="message">نص الرسالة</param>
        /// <param name="title">عنوان الرسالة</param>
        /// <param name="icon">أيقونة الرسالة</param>
        private static void ShowErrorMessage(string message, string title, string icon)
        {
            // في بيئة خدمات، نقوم بتسجيل الرسالة فقط
            Console.WriteLine($"[{icon}] {title}: {message}");

            // في بيئة واجهة المستخدم، يمكن استخدام MessageBox
            // لكن هنا نكتفي بالتسجيل فقط لتجنب الاعتماد على System.Windows
        }

        /// <summary>
        /// تسجيل الاستثناء في ملف السجل
        /// </summary>
        /// <param name="exception">الاستثناء المراد تسجيله</param>
        public void LogException(Exception exception)
        {
            try
            {
                // التأكد من وجود مجلد السجلات
                if (!Directory.Exists(LogDirectory))
                {
                    Directory.CreateDirectory(LogDirectory);
                }

                string logFileName = Path.Combine(LogDirectory, $"Error_{DateTime.Now:yyyy-MM-dd}.log");

                StringBuilder logMessage = new StringBuilder();
                logMessage.AppendLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] Exception Details:");
                logMessage.AppendLine($"Type: {exception.GetType().FullName}");

                if (exception is InjazAccException injazException)
                {
                    logMessage.AppendLine($"Error Code: {injazException.ErrorCode}");
                    logMessage.AppendLine($"Severity: {injazException.Severity}");
                }

                logMessage.AppendLine($"Message: {exception.Message}");
                logMessage.AppendLine($"Stack Trace: {exception.StackTrace}");

                if (exception.InnerException != null)
                {
                    logMessage.AppendLine("Inner Exception:");
                    logMessage.AppendLine($"Type: {exception.InnerException.GetType().FullName}");
                    logMessage.AppendLine($"Message: {exception.InnerException.Message}");
                    logMessage.AppendLine($"Stack Trace: {exception.InnerException.StackTrace}");
                }

                logMessage.AppendLine(new string('-', 80));

                lock (LogLock)
                {
                    File.AppendAllText(logFileName, logMessage.ToString());
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل التسجيل، نحاول تسجيل الخطأ في وحدة التحكم
                try
                {
                    Console.WriteLine($"[ERROR] Failed to log exception: {ex.Message}\nOriginal exception: {exception.Message}");
                }
                catch
                {
                    // لا يمكن فعل المزيد إذا فشل التسجيل في وحدة التحكم أيضًا
                }
            }
        }

        /// <summary>
        /// تسجيل الاستثناء في ملف السجل بشكل ثابت (static)
        /// </summary>
        /// <param name="exception">الاستثناء المراد تسجيله</param>
        public static void LogExceptionStatic(Exception exception)
        {
            new ExceptionHandlingService().LogException(exception);
        }
    }
}
