﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FF93903853B331090C0FC8FA5F68B7257D1736E5"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using InjazAcc.UI;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InjazAcc.UI {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 98 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListViewItem menuDashboard;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListViewItem menuSales;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListViewItem menuPurchases;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListViewItem menuInventory;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListViewItem menuCustomers;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListViewItem menuSuppliers;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListViewItem menuAccounting;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListViewItem menuReports;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListViewItem menuSettings;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtUserName;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtUserRole;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnMinimize;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnMaximize;
        
        #line default
        #line hidden
        
        
        #line 273 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCloseMain;
        
        #line default
        #line hidden
        
        
        #line 281 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.PackIcon currentPageIcon;
        
        #line default
        #line hidden
        
        
        #line 283 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock currentPageTitle;
        
        #line default
        #line hidden
        
        
        #line 288 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSearch;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Frame mainFrame;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InjazAcc.UI;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 21 "..\..\..\MainWindow.xaml"
            ((InjazAcc.UI.MainWindow)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.Window_MouseDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.menuDashboard = ((System.Windows.Controls.ListViewItem)(target));
            
            #line 98 "..\..\..\MainWindow.xaml"
            this.menuDashboard.Selected += new System.Windows.RoutedEventHandler(this.menuItem_Selected);
            
            #line default
            #line hidden
            return;
            case 3:
            this.menuSales = ((System.Windows.Controls.ListViewItem)(target));
            
            #line 112 "..\..\..\MainWindow.xaml"
            this.menuSales.Selected += new System.Windows.RoutedEventHandler(this.menuItem_Selected);
            
            #line default
            #line hidden
            return;
            case 4:
            this.menuPurchases = ((System.Windows.Controls.ListViewItem)(target));
            
            #line 126 "..\..\..\MainWindow.xaml"
            this.menuPurchases.Selected += new System.Windows.RoutedEventHandler(this.menuItem_Selected);
            
            #line default
            #line hidden
            return;
            case 5:
            this.menuInventory = ((System.Windows.Controls.ListViewItem)(target));
            
            #line 140 "..\..\..\MainWindow.xaml"
            this.menuInventory.Selected += new System.Windows.RoutedEventHandler(this.menuItem_Selected);
            
            #line default
            #line hidden
            return;
            case 6:
            this.menuCustomers = ((System.Windows.Controls.ListViewItem)(target));
            
            #line 154 "..\..\..\MainWindow.xaml"
            this.menuCustomers.Selected += new System.Windows.RoutedEventHandler(this.menuItem_Selected);
            
            #line default
            #line hidden
            return;
            case 7:
            this.menuSuppliers = ((System.Windows.Controls.ListViewItem)(target));
            
            #line 168 "..\..\..\MainWindow.xaml"
            this.menuSuppliers.Selected += new System.Windows.RoutedEventHandler(this.menuItem_Selected);
            
            #line default
            #line hidden
            return;
            case 8:
            this.menuAccounting = ((System.Windows.Controls.ListViewItem)(target));
            
            #line 182 "..\..\..\MainWindow.xaml"
            this.menuAccounting.Selected += new System.Windows.RoutedEventHandler(this.menuItem_Selected);
            
            #line default
            #line hidden
            return;
            case 9:
            this.menuReports = ((System.Windows.Controls.ListViewItem)(target));
            
            #line 196 "..\..\..\MainWindow.xaml"
            this.menuReports.Selected += new System.Windows.RoutedEventHandler(this.menuItem_Selected);
            
            #line default
            #line hidden
            return;
            case 10:
            this.menuSettings = ((System.Windows.Controls.ListViewItem)(target));
            
            #line 210 "..\..\..\MainWindow.xaml"
            this.menuSettings.Selected += new System.Windows.RoutedEventHandler(this.menuItem_Selected);
            
            #line default
            #line hidden
            return;
            case 11:
            this.txtUserName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.txtUserRole = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            
            #line 242 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnLogout_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.btnMinimize = ((System.Windows.Controls.Button)(target));
            
            #line 267 "..\..\..\MainWindow.xaml"
            this.btnMinimize.Click += new System.Windows.RoutedEventHandler(this.btnMinimize_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.btnMaximize = ((System.Windows.Controls.Button)(target));
            
            #line 270 "..\..\..\MainWindow.xaml"
            this.btnMaximize.Click += new System.Windows.RoutedEventHandler(this.btnMaximize_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.btnCloseMain = ((System.Windows.Controls.Button)(target));
            
            #line 273 "..\..\..\MainWindow.xaml"
            this.btnCloseMain.Click += new System.Windows.RoutedEventHandler(this.btnCloseMain_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.currentPageIcon = ((MaterialDesignThemes.Wpf.PackIcon)(target));
            return;
            case 18:
            this.currentPageTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.btnSearch = ((System.Windows.Controls.Button)(target));
            
            #line 288 "..\..\..\MainWindow.xaml"
            this.btnSearch.Click += new System.Windows.RoutedEventHandler(this.btnSearch_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.mainFrame = ((System.Windows.Controls.Frame)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

