using System;

namespace InjazAcc.Core.Exceptions
{
    /// <summary>
    /// استثناء مخصص لأخطاء المصادقة
    /// </summary>
    public class AuthenticationException : InjazAccException
    {
        /// <summary>
        /// إنشاء استثناء جديد بدون رسالة
        /// </summary>
        public AuthenticationException() : base("فشل في عملية المصادقة", "ERR-AUTH-001")
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        public AuthenticationException(string message) : base(message, "ERR-AUTH-001")
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        public AuthenticationException(string message, string errorCode) : base(message, errorCode)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public AuthenticationException(string message, Exception innerException) : base(message, "ERR-AUTH-001", innerException)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public AuthenticationException(string message, string errorCode, Exception innerException) : base(message, errorCode, innerException)
        {
        }
    }
}
