using InjazAcc.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace InjazAcc.Core.Interfaces
{
    /// <summary>
    /// واجهة خدمة المصادقة والتحقق من الصلاحيات
    /// </summary>
    public interface IAuthService
    {
        // المصادقة
        Task<User> AuthenticateAsync(string username, string password);
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
        Task<string> GeneratePasswordHashAsync(string password);
        Task<bool> VerifyPasswordAsync(string passwordHash, string password);
        
        // إدارة المستخدمين
        Task<User> GetUserByIdAsync(int id);
        Task<User> GetUserByUsernameAsync(string username);
        Task<IEnumerable<User>> GetAllUsersAsync();
        Task<User> CreateUserAsync(User user, string password);
        Task UpdateUserAsync(User user);
        Task DeleteUserAsync(int userId);
        
        // إدارة الأدوار والصلاحيات
        Task<IEnumerable<Role>> GetUserRolesAsync(int userId);
        Task<IEnumerable<Permission>> GetUserPermissionsAsync(int userId);
        Task<bool> HasPermissionAsync(int userId, string permissionName);
        Task AssignRoleToUserAsync(int userId, int roleId);
        Task RemoveRoleFromUserAsync(int userId, int roleId);
    }
}
