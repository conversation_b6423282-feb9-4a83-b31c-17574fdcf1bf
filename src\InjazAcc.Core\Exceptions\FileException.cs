using System;

namespace InjazAcc.Core.Exceptions
{
    /// <summary>
    /// استثناء مخصص لأخطاء الملفات
    /// </summary>
    public class FileException : InjazAccException
    {
        /// <summary>
        /// مسار الملف المرتبط بالاستثناء
        /// </summary>
        public string FilePath { get; }

        /// <summary>
        /// إنشاء استثناء جديد بدون رسالة
        /// </summary>
        public FileException() : base("حدث خطأ أثناء التعامل مع الملف", ErrorCodes.FileNotFound)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        public FileException(string message) : base(message, ErrorCodes.FileNotFound)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ومسار ملف
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="filePath">مسار الملف</param>
        /// <remarks>
        /// تم تعطيل هذا البناء لتجنب التعارض مع البناء الآخر الذي يأخذ نفس أنواع المعلمات
        /// استخدم البناء الذي يأخذ رمز خطأ بدلاً من ذلك
        /// </remarks>
        //public FileException(string message, string filePath) : base(message, ErrorCodes.FileNotFound)
        //{
        //    FilePath = filePath;
        //}

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        public FileException(string message, string errorCode) : base(message, errorCode)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ ومسار ملف
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        /// <param name="filePath">مسار الملف</param>
        public FileException(string message, string errorCode, string filePath) : base(message, errorCode)
        {
            FilePath = filePath;
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public FileException(string message, Exception innerException) : base(message, ErrorCodes.FileNotFound, innerException)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ومسار ملف واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public FileException(string message, string filePath, Exception innerException) : base(message, ErrorCodes.FileNotFound, innerException)
        {
            FilePath = filePath;
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        /// <remarks>
        /// تم تعطيل هذا البناء لتجنب التعارض مع البناء الآخر الذي يأخذ نفس أنواع المعلمات
        /// </remarks>
        //public FileException(string message, string errorCode, Exception innerException) : base(message, errorCode, innerException)
        //{
        //}

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ ومسار ملف واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public FileException(string message, string errorCode, string filePath, Exception innerException) : base(message, errorCode, innerException)
        {
            FilePath = filePath;
        }

        /// <summary>
        /// الحصول على رسالة خطأ مفصلة تتضمن مسار الملف
        /// </summary>
        /// <returns>رسالة الخطأ المفصلة</returns>
        public override string GetDetailedMessage()
        {
            var baseMessage = base.GetDetailedMessage();

            if (string.IsNullOrEmpty(FilePath))
            {
                return baseMessage;
            }

            return $"{baseMessage}\nمسار الملف: {FilePath}";
        }
    }
}
