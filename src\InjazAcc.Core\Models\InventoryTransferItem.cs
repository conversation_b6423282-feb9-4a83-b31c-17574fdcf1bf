namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج عنصر في عملية التحويل المخزني
    /// </summary>
    public class InventoryTransferItem
    {
        public int Id { get; set; }
        public int InventoryTransferId { get; set; }
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public string Notes { get; set; }
        
        // العلاقات
        public virtual InventoryTransfer InventoryTransfer { get; set; }
        public virtual Product Product { get; set; }
    }
}
