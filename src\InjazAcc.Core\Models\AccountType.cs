namespace InjazAcc.Core.Models
{
    /// <summary>
    /// أنواع الحسابات المحاسبية
    /// </summary>
    public enum AccountType
    {
        /// <summary>
        /// أصول
        /// </summary>
        Asset = 1,
        
        /// <summary>
        /// التزامات
        /// </summary>
        Liability = 2,
        
        /// <summary>
        /// حقوق ملكية
        /// </summary>
        Equity = 3,
        
        /// <summary>
        /// إيرادات
        /// </summary>
        Revenue = 4,
        
        /// <summary>
        /// مصروفات
        /// </summary>
        Expense = 5,
        
        /// <summary>
        /// تكلفة المبيعات
        /// </summary>
        CostOfSales = 6
    }
}
