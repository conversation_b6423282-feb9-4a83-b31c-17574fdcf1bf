using System;
using System.Windows;

namespace InjazAcc.UI.Views.Suppliers
{
    /// <summary>
    /// Interaction logic for SupplierWindow.xaml
    /// </summary>
    public partial class SupplierWindow : Window
    {
        private bool _isEditMode = false;
        private Supplier _supplier;

        public Supplier Supplier => _supplier;

        public SupplierWindow()
        {
            InitializeComponent();
            _supplier = new Supplier();
            InitializeControls();
        }

        public SupplierWindow(Supplier supplier)
        {
            InitializeComponent();
            _isEditMode = true;
            _supplier = new Supplier
            {
                Code = supplier.Code,
                Name = supplier.Name,
                Phone = supplier.Phone,
                Email = supplier.Email,
                Address = supplier.Address,
                Balance = supplier.Balance,
                TotalPurchases = supplier.TotalPurchases
            };
            InitializeControls();
            LoadSupplierData();
        }

        private void InitializeControls()
        {
            // تعيين القيم الافتراضية
            if (!_isEditMode)
            {
                txtCode.Text = GenerateNewCode();
                txtBalance.Text = "0";
                txtPaymentPeriod.Text = "30";
                txtTax.Text = "15";
                cmbType.SelectedIndex = 0;
            }

            // تغيير عنوان النافذة في حالة التعديل
            if (_isEditMode)
            {
                txtWindowTitle.Text = "تعديل بيانات المورد";
                this.Title = "تعديل بيانات المورد";
            }
        }

        private string GenerateNewCode()
        {
            // توليد رمز جديد (في التطبيق الحقيقي سيتم جلب الرمز من قاعدة البيانات)
            Random random = new Random();
            return $"S{random.Next(1000, 9999)}";
        }

        private void LoadSupplierData()
        {
            // تحميل بيانات المورد في حالة التعديل
            if (_supplier != null)
            {
                txtCode.Text = _supplier.Code;
                txtName.Text = _supplier.Name;
                txtPhone.Text = _supplier.Phone;
                txtEmail.Text = _supplier.Email;
                txtAddress.Text = _supplier.Address;
                txtBalance.Text = _supplier.Balance.ToString();
                txtPaymentPeriod.Text = "30"; // قيمة افتراضية لفترة السداد
                txtTax.Text = "15"; // قيمة افتراضية للضريبة
                
                // تحديد نوع المورد بناءً على الاسم (مجرد مثال)
                if (_supplier.Name.Contains("شركة"))
                {
                    cmbType.SelectedIndex = 0; // شركة
                }
                else if (_supplier.Name.Contains("مؤسسة"))
                {
                    cmbType.SelectedIndex = 1; // مؤسسة
                }
                else
                {
                    cmbType.SelectedIndex = 2; // فرد
                }
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtCode.Text))
                {
                    MessageBox.Show("الرجاء إدخال رمز المورد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtCode.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم المورد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtName.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPhone.Text))
                {
                    MessageBox.Show("الرجاء إدخال رقم هاتف المورد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtPhone.Focus();
                    return;
                }

                if (!double.TryParse(txtBalance.Text, out double balance))
                {
                    MessageBox.Show("الرجاء إدخال رصيد صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtBalance.Focus();
                    return;
                }

                if (!int.TryParse(txtPaymentPeriod.Text, out int paymentPeriod) || paymentPeriod < 0)
                {
                    MessageBox.Show("الرجاء إدخال فترة سداد صحيحة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtPaymentPeriod.Focus();
                    return;
                }

                if (!double.TryParse(txtTax.Text, out double tax) || tax < 0 || tax > 100)
                {
                    MessageBox.Show("الرجاء إدخال نسبة ضريبة صحيحة (0-100)", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtTax.Focus();
                    return;
                }

                // تحديث بيانات المورد
                _supplier.Code = txtCode.Text;
                _supplier.Name = txtName.Text;
                _supplier.Phone = txtPhone.Text;
                _supplier.Email = txtEmail.Text;
                _supplier.Address = txtAddress.Text;
                _supplier.Balance = balance;

                // إغلاق النافذة بنجاح
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إلغاء العملية وإغلاق النافذة
            this.DialogResult = false;
        }
    }
}
