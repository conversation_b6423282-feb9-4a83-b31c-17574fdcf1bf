using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using InjazAcc.Core.Interfaces;
using InjazAcc.Core.Models;
using InjazAcc.Core.Models.FinancialStatements;

namespace InjazAcc.Services
{
    /// <summary>
    /// خدمة الحسابات الختامية
    /// </summary>
    public class FinancialStatementsService : IFinancialStatementsService
    {
        private readonly IUnitOfWork _unitOfWork;
        
        public FinancialStatementsService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        
        /// <summary>
        /// الحصول على ميزان المراجعة
        /// </summary>
        public async Task<FinancialStatementResult> GetTrialBalanceAsync(DateTime fromDate, DateTime toDate)
        {
            // في التطبيق الحقيقي، سيتم استرجاع البيانات من قاعدة البيانات
            // هنا نستخدم بيانات تجريبية للعرض
            
            var result = new FinancialStatementResult
            {
                FromDate = fromDate,
                ToDate = toDate,
                TrialBalanceEntries = new List<TrialBalanceEntry>
                {
                    new TrialBalanceEntry { AccountCode = "1010", AccountName = "الصندوق", AccountType = AccountType.Asset, DebitBalance = 35000, CreditBalance = 0, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "1020", AccountName = "البنك", AccountType = AccountType.Asset, DebitBalance = 120000, CreditBalance = 0, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "1030", AccountName = "المخزون", AccountType = AccountType.Asset, DebitBalance = 180000, CreditBalance = 0, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "1040", AccountName = "العملاء", AccountType = AccountType.Asset, DebitBalance = 65000, CreditBalance = 0, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "1050", AccountName = "أثاث ومعدات", AccountType = AccountType.Asset, DebitBalance = 120000, CreditBalance = 0, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "2010", AccountName = "الموردين", AccountType = AccountType.Liability, DebitBalance = 0, CreditBalance = 75000, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "2020", AccountName = "قروض قصيرة الأجل", AccountType = AccountType.Liability, DebitBalance = 0, CreditBalance = 100000, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "2030", AccountName = "مصروفات مستحقة", AccountType = AccountType.Liability, DebitBalance = 0, CreditBalance = 25000, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "3010", AccountName = "رأس المال", AccountType = AccountType.Equity, DebitBalance = 0, CreditBalance = 350000, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "3020", AccountName = "الأرباح المحتجزة", AccountType = AccountType.Equity, DebitBalance = 0, CreditBalance = 25000, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "4010", AccountName = "المبيعات", AccountType = AccountType.Revenue, DebitBalance = 0, CreditBalance = 250000, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "4020", AccountName = "إيرادات أخرى", AccountType = AccountType.Revenue, DebitBalance = 0, CreditBalance = 15000, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "5010", AccountName = "تكلفة المبيعات", AccountType = AccountType.CostOfSales, DebitBalance = 150000, CreditBalance = 0, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "5020", AccountName = "رواتب وأجور", AccountType = AccountType.Expense, DebitBalance = 85000, CreditBalance = 0, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "5030", AccountName = "إيجارات", AccountType = AccountType.Expense, DebitBalance = 35000, CreditBalance = 0, BalanceDate = toDate },
                    new TrialBalanceEntry { AccountCode = "5040", AccountName = "مصروفات عمومية وإدارية", AccountType = AccountType.Expense, DebitBalance = 50000, CreditBalance = 0, BalanceDate = toDate }
                }
            };
            
            // حساب الإجماليات
            result.TotalDebit = result.TrialBalanceEntries.Sum(e => e.DebitBalance);
            result.TotalCredit = result.TrialBalanceEntries.Sum(e => e.CreditBalance);
            
            return result;
        }
        
        /// <summary>
        /// الحصول على قائمة الدخل
        /// </summary>
        public async Task<FinancialStatementResult> GetIncomeStatementAsync(DateTime fromDate, DateTime toDate)
        {
            // في التطبيق الحقيقي، سيتم استرجاع البيانات من قاعدة البيانات
            // هنا نستخدم بيانات تجريبية للعرض
            
            var result = new FinancialStatementResult
            {
                FromDate = fromDate,
                ToDate = toDate,
                IncomeStatementEntries = new List<IncomeStatementEntry>
                {
                    // الإيرادات
                    new IncomeStatementEntry { Description = "الإيرادات", Type = IncomeStatementEntryType.Revenue, IsHeader = true, Level = 0 },
                    new IncomeStatementEntry { Description = "المبيعات", Type = IncomeStatementEntryType.Revenue, Amount = 250000, AccountCode = "4010", Level = 1 },
                    new IncomeStatementEntry { Description = "إيرادات أخرى", Type = IncomeStatementEntryType.Revenue, Amount = 15000, AccountCode = "4020", Level = 1 },
                    new IncomeStatementEntry { Description = "إجمالي الإيرادات", Type = IncomeStatementEntryType.Revenue, Amount = 265000, IsTotal = true, Level = 0 },
                    
                    // تكلفة المبيعات
                    new IncomeStatementEntry { Description = "تكلفة المبيعات", Type = IncomeStatementEntryType.CostOfSales, Amount = -150000, AccountCode = "5010", Level = 1 },
                    new IncomeStatementEntry { Description = "مجمل الربح", Type = IncomeStatementEntryType.GrossProfit, Amount = 115000, IsTotal = true, Level = 0 },
                    
                    // المصروفات
                    new IncomeStatementEntry { Description = "المصروفات", Type = IncomeStatementEntryType.Expense, IsHeader = true, Level = 0 },
                    new IncomeStatementEntry { Description = "رواتب وأجور", Type = IncomeStatementEntryType.Expense, Amount = -85000, AccountCode = "5020", Level = 1 },
                    new IncomeStatementEntry { Description = "إيجارات", Type = IncomeStatementEntryType.Expense, Amount = -35000, AccountCode = "5030", Level = 1 },
                    new IncomeStatementEntry { Description = "مصروفات عمومية وإدارية", Type = IncomeStatementEntryType.Expense, Amount = -50000, AccountCode = "5040", Level = 1 },
                    new IncomeStatementEntry { Description = "إجمالي المصروفات", Type = IncomeStatementEntryType.Expense, Amount = -170000, IsTotal = true, Level = 0 },
                    
                    // صافي الربح
                    new IncomeStatementEntry { Description = "صافي الربح (الخسارة)", Type = IncomeStatementEntryType.NetProfit, Amount = -55000, IsTotal = true, Level = 0 }
                }
            };
            
            // حساب الإجماليات
            result.TotalRevenues = result.IncomeStatementEntries
                .Where(e => e.Type == IncomeStatementEntryType.Revenue && !e.IsHeader && !e.IsTotal)
                .Sum(e => e.Amount);
                
            result.TotalExpenses = result.IncomeStatementEntries
                .Where(e => (e.Type == IncomeStatementEntryType.Expense || e.Type == IncomeStatementEntryType.CostOfSales) && !e.IsHeader && !e.IsTotal)
                .Sum(e => e.Amount);
                
            result.NetProfit = result.TotalRevenues + result.TotalExpenses; // المصروفات سالبة بالفعل
            
            return result;
        }
        
        /// <summary>
        /// الحصول على الميزانية العمومية
        /// </summary>
        public async Task<FinancialStatementResult> GetBalanceSheetAsync(DateTime asOfDate)
        {
            // في التطبيق الحقيقي، سيتم استرجاع البيانات من قاعدة البيانات
            // هنا نستخدم بيانات تجريبية للعرض
            
            var result = new FinancialStatementResult
            {
                FromDate = asOfDate,
                ToDate = asOfDate,
                AssetEntries = new List<BalanceSheetEntry>
                {
                    // الأصول المتداولة
                    new BalanceSheetEntry { Description = "الأصول المتداولة", Type = BalanceSheetEntryType.CurrentAsset, IsHeader = true, Level = 0 },
                    new BalanceSheetEntry { Description = "الصندوق", Type = BalanceSheetEntryType.CurrentAsset, Amount = 35000, AccountCode = "1010", Level = 1 },
                    new BalanceSheetEntry { Description = "البنك", Type = BalanceSheetEntryType.CurrentAsset, Amount = 120000, AccountCode = "1020", Level = 1 },
                    new BalanceSheetEntry { Description = "المخزون", Type = BalanceSheetEntryType.CurrentAsset, Amount = 180000, AccountCode = "1030", Level = 1 },
                    new BalanceSheetEntry { Description = "العملاء", Type = BalanceSheetEntryType.CurrentAsset, Amount = 65000, AccountCode = "1040", Level = 1 },
                    new BalanceSheetEntry { Description = "إجمالي الأصول المتداولة", Type = BalanceSheetEntryType.CurrentAsset, Amount = 400000, IsTotal = true, Level = 0 },
                    
                    // الأصول الثابتة
                    new BalanceSheetEntry { Description = "الأصول الثابتة", Type = BalanceSheetEntryType.FixedAsset, IsHeader = true, Level = 0 },
                    new BalanceSheetEntry { Description = "أثاث ومعدات", Type = BalanceSheetEntryType.FixedAsset, Amount = 120000, AccountCode = "1050", Level = 1 },
                    new BalanceSheetEntry { Description = "إجمالي الأصول الثابتة", Type = BalanceSheetEntryType.FixedAsset, Amount = 120000, IsTotal = true, Level = 0 },
                    
                    // إجمالي الأصول
                    new BalanceSheetEntry { Description = "إجمالي الأصول", Type = BalanceSheetEntryType.TotalAssets, Amount = 520000, IsTotal = true, Level = 0 }
                },
                LiabilitiesEquityEntries = new List<BalanceSheetEntry>
                {
                    // الخصوم المتداولة
                    new BalanceSheetEntry { Description = "الخصوم المتداولة", Type = BalanceSheetEntryType.CurrentLiability, IsHeader = true, Level = 0 },
                    new BalanceSheetEntry { Description = "الموردين", Type = BalanceSheetEntryType.CurrentLiability, Amount = 75000, AccountCode = "2010", Level = 1 },
                    new BalanceSheetEntry { Description = "قروض قصيرة الأجل", Type = BalanceSheetEntryType.CurrentLiability, Amount = 100000, AccountCode = "2020", Level = 1 },
                    new BalanceSheetEntry { Description = "مصروفات مستحقة", Type = BalanceSheetEntryType.CurrentLiability, Amount = 25000, AccountCode = "2030", Level = 1 },
                    new BalanceSheetEntry { Description = "إجمالي الخصوم المتداولة", Type = BalanceSheetEntryType.CurrentLiability, Amount = 200000, IsTotal = true, Level = 0 },
                    
                    // حقوق الملكية
                    new BalanceSheetEntry { Description = "حقوق الملكية", Type = BalanceSheetEntryType.Equity, IsHeader = true, Level = 0 },
                    new BalanceSheetEntry { Description = "رأس المال", Type = BalanceSheetEntryType.Equity, Amount = 350000, AccountCode = "3010", Level = 1 },
                    new BalanceSheetEntry { Description = "الأرباح المحتجزة", Type = BalanceSheetEntryType.Equity, Amount = 25000, AccountCode = "3020", Level = 1 },
                    new BalanceSheetEntry { Description = "صافي ربح (خسارة) الفترة", Type = BalanceSheetEntryType.Equity, Amount = -55000, Level = 1 },
                    new BalanceSheetEntry { Description = "إجمالي حقوق الملكية", Type = BalanceSheetEntryType.Equity, Amount = 320000, IsTotal = true, Level = 0 },
                    
                    // إجمالي الخصوم وحقوق الملكية
                    new BalanceSheetEntry { Description = "إجمالي الخصوم وحقوق الملكية", Type = BalanceSheetEntryType.TotalLiabilitiesEquity, Amount = 520000, IsTotal = true, Level = 0 }
                }
            };
            
            // حساب الإجماليات
            result.TotalAssets = result.AssetEntries
                .Where(e => e.Type == BalanceSheetEntryType.TotalAssets)
                .Sum(e => e.Amount);
                
            result.TotalLiabilitiesEquity = result.LiabilitiesEquityEntries
                .Where(e => e.Type == BalanceSheetEntryType.TotalLiabilitiesEquity)
                .Sum(e => e.Amount);
            
            return result;
        }
        
        /// <summary>
        /// الحصول على جميع الحسابات الختامية
        /// </summary>
        public async Task<FinancialStatementResult> GetAllFinancialStatementsAsync(DateTime fromDate, DateTime toDate)
        {
            var trialBalance = await GetTrialBalanceAsync(fromDate, toDate);
            var incomeStatement = await GetIncomeStatementAsync(fromDate, toDate);
            var balanceSheet = await GetBalanceSheetAsync(toDate);
            
            return new FinancialStatementResult
            {
                FromDate = fromDate,
                ToDate = toDate,
                TrialBalanceEntries = trialBalance.TrialBalanceEntries,
                TotalDebit = trialBalance.TotalDebit,
                TotalCredit = trialBalance.TotalCredit,
                IncomeStatementEntries = incomeStatement.IncomeStatementEntries,
                TotalRevenues = incomeStatement.TotalRevenues,
                TotalExpenses = incomeStatement.TotalExpenses,
                NetProfit = incomeStatement.NetProfit,
                AssetEntries = balanceSheet.AssetEntries,
                LiabilitiesEquityEntries = balanceSheet.LiabilitiesEquityEntries,
                TotalAssets = balanceSheet.TotalAssets,
                TotalLiabilitiesEquity = balanceSheet.TotalLiabilitiesEquity
            };
        }
        
        /// <summary>
        /// إقفال الحسابات في نهاية السنة المالية
        /// </summary>
        public async Task<bool> CloseAccountsForFiscalYearAsync(int fiscalYearId, DateTime closingDate, int userId)
        {
            try
            {
                // في التطبيق الحقيقي، سيتم تنفيذ الخطوات التالية:
                // 1. التحقق من توازن ميزان المراجعة
                // 2. إنشاء قيد إقفال للإيرادات والمصروفات
                // 3. ترحيل صافي الربح/الخسارة إلى حساب الأرباح المحتجزة
                // 4. تحديث حالة السنة المالية إلى "مغلقة"
                
                // هنا نقوم فقط بمحاكاة نجاح العملية
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
