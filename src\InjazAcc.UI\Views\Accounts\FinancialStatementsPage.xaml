<Page x:Class="InjazAcc.UI.Views.Accounts.FinancialStatementsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Accounts"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="900"
      Title="الحسابات الختامية"
      FlowDirection="RightToLeft"
      FontFamily="Arial"
      TextElement.FontSize="14"
      TextElement.FontWeight="Regular"
      TextElement.Foreground="{DynamicResource MaterialDesignBody}"
      Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة وزر الرجوع -->
        <Grid Grid.Row="0" Margin="20,20,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0" Style="{StaticResource MaterialDesignFlatButton}"
                    ToolTip="الرجوع للحسابات" Click="btnBackToAccounts_Click"
                    HorizontalAlignment="Left" Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ArrowRight" Width="24" Height="24" VerticalAlignment="Center"/>
                    <TextBlock Text="الرجوع للحسابات" VerticalAlignment="Center" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>

            <TextBlock Grid.Column="1" Text="الحسابات الختامية" Style="{StaticResource PageTitle}"/>
        </Grid>

        <!-- أدوات التحكم -->
        <Grid Grid.Row="1" Margin="20,0,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- اختيار الفترة -->
            <TextBlock Grid.Column="0" Text="الفترة:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <ComboBox Grid.Column="1" x:Name="cmbPeriod" Width="150" SelectedIndex="0" Margin="0,0,20,0">
                <ComboBoxItem Content="الربع الحالي"/>
                <ComboBoxItem Content="الربع السابق"/>
                <ComboBoxItem Content="السنة الحالية"/>
                <ComboBoxItem Content="السنة السابقة"/>
                <ComboBoxItem Content="فترة مخصصة"/>
            </ComboBox>

            <!-- التواريخ المخصصة -->
            <StackPanel Grid.Column="2" Orientation="Horizontal" x:Name="spCustomDates" Visibility="Collapsed">
                <DatePicker x:Name="dpFromDate" Width="120" materialDesign:HintAssist.Hint="من تاريخ" Margin="0,0,10,0"/>
                <DatePicker x:Name="dpToDate" Width="120" materialDesign:HintAssist.Hint="إلى تاريخ" Margin="0,0,10,0"/>
            </StackPanel>

            <!-- أزرار الإجراءات -->
            <StackPanel Grid.Column="4" Orientation="Horizontal">
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="عرض" Margin="0,0,10,0" Click="btnShow_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="طباعة" Margin="0,0,10,0" Click="btnPrint_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="تصدير Excel" Margin="0,0,10,0" Click="btnExport_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedAccentButton}" Content="إقفال السنة المالية" Click="btnCloseFiscalYear_Click"/>
            </StackPanel>
        </Grid>

        <!-- التبويبات -->
        <TabControl Grid.Row="2" Margin="20,0,20,20">
            <!-- ميزان المراجعة -->
            <TabItem Header="ميزان المراجعة">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- جدول ميزان المراجعة -->
                    <DataGrid Grid.Row="0" x:Name="dgTrialBalance" AutoGenerateColumns="False" CanUserAddRows="False"
                              Style="{StaticResource DataGridStyle}" Margin="0,10,0,10">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الحساب" Binding="{Binding AccountNumber}" Width="100"/>
                            <DataGridTextColumn Header="اسم الحساب" Binding="{Binding AccountName}" Width="*"/>
                            <DataGridTextColumn Header="رصيد مدين" Binding="{Binding DebitBalance, StringFormat=N2}" Width="150"/>
                            <DataGridTextColumn Header="رصيد دائن" Binding="{Binding CreditBalance, StringFormat=N2}" Width="150"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- الإجماليات -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="150"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="الإجمالي" FontWeight="Bold" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1" x:Name="txtTotalDebit" Text="0.00" FontWeight="Bold" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="2" x:Name="txtTotalCredit" Text="0.00" FontWeight="Bold" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Grid>
                </Grid>
            </TabItem>

            <!-- قائمة الدخل -->
            <TabItem Header="قائمة الدخل">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- جدول قائمة الدخل -->
                    <DataGrid Grid.Row="0" x:Name="dgIncomeStatement" AutoGenerateColumns="False" CanUserAddRows="False"
                              Style="{StaticResource DataGridStyle}" Margin="0,10,0,10">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="البيان" Binding="{Binding Description}" Width="*"/>
                            <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="150"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- النتيجة النهائية -->
                    <Border Grid.Row="1" Background="#4CAF50" Padding="10" CornerRadius="5">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="150"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="صافي الربح (الخسارة)" Foreground="White" FontWeight="Bold" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="1" x:Name="txtNetIncome" Text="0.00" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Grid>
                    </Border>
                </Grid>
            </TabItem>

            <!-- الميزانية العمومية -->
            <TabItem Header="الميزانية العمومية">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- جدول الميزانية العمومية -->
                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- الأصول -->
                        <Grid Grid.Column="0" Margin="0,10,5,10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="الأصول" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>

                            <DataGrid Grid.Row="1" x:Name="dgAssets" AutoGenerateColumns="False" CanUserAddRows="False"
                                      Style="{StaticResource DataGridStyle}">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="البيان" Binding="{Binding Description}" Width="*"/>
                                    <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="150"/>
                                </DataGrid.Columns>
                            </DataGrid>

                            <Border Grid.Row="2" Background="#2196F3" Padding="10" CornerRadius="5" Margin="0,10,0,0">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="150"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0" Text="إجمالي الأصول" Foreground="White" FontWeight="Bold" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="1" x:Name="txtTotalAssets" Text="0.00" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Grid>
                            </Border>
                        </Grid>

                        <!-- الخصوم وحقوق الملكية -->
                        <Grid Grid.Column="1" Margin="5,10,0,10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="الخصوم وحقوق الملكية" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>

                            <DataGrid Grid.Row="1" x:Name="dgLiabilitiesEquity" AutoGenerateColumns="False" CanUserAddRows="False"
                                      Style="{StaticResource DataGridStyle}">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="البيان" Binding="{Binding Description}" Width="*"/>
                                    <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="150"/>
                                </DataGrid.Columns>
                            </DataGrid>

                            <Border Grid.Row="2" Background="#FF9800" Padding="10" CornerRadius="5" Margin="0,10,0,0">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="150"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0" Text="إجمالي الخصوم وحقوق الملكية" Foreground="White" FontWeight="Bold" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="1" x:Name="txtTotalLiabilitiesEquity" Text="0.00" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Grid>
                            </Border>
                        </Grid>
                    </Grid>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</Page>
