<Page x:Class="InjazAcc.UI.Views.DashboardPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="1100"
      Title="لوحة التحكم">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة -->
        <TextBlock Grid.Row="0" Text="لوحة التحكم" Style="{StaticResource PageTitle}"/>

        <!-- بطاقات الإحصائيات -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- إجمالي المبيعات -->
            <materialDesign:Card Grid.Column="0" Margin="5" Padding="15" Background="{DynamicResource PrimaryHueLightBrush}" UniformCornerRadius="8">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <materialDesign:PackIcon Grid.Row="0" Kind="CartOutline" Width="32" Height="32" HorizontalAlignment="Right" Foreground="White"/>
                    <TextBlock Grid.Row="1" Text="إجمالي المبيعات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,10,0,5" Foreground="White"/>
                    <TextBlock Grid.Row="2" Text="25,000 ر.س" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right" Foreground="White"/>
                </Grid>
            </materialDesign:Card>

            <!-- إجمالي المشتريات -->
            <materialDesign:Card Grid.Column="1" Margin="5" Padding="15" Background="#FF5722" UniformCornerRadius="8">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <materialDesign:PackIcon Grid.Row="0" Kind="ShoppingOutline" Width="32" Height="32" HorizontalAlignment="Right" Foreground="White"/>
                    <TextBlock Grid.Row="1" Text="إجمالي المشتريات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,10,0,5" Foreground="White"/>
                    <TextBlock Grid.Row="2" Text="18,500 ر.س" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right" Foreground="White"/>
                </Grid>
            </materialDesign:Card>

            <!-- عدد العملاء -->
            <materialDesign:Card Grid.Column="2" Margin="5" Padding="15" Background="#4CAF50" UniformCornerRadius="8">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <materialDesign:PackIcon Grid.Row="0" Kind="AccountMultipleOutline" Width="32" Height="32" HorizontalAlignment="Right" Foreground="White"/>
                    <TextBlock Grid.Row="1" Text="عدد العملاء" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,10,0,5" Foreground="White"/>
                    <TextBlock Grid.Row="2" Text="45" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right" Foreground="White"/>
                </Grid>
            </materialDesign:Card>

            <!-- عدد المنتجات -->
            <materialDesign:Card Grid.Column="3" Margin="5" Padding="15" Background="#2196F3" UniformCornerRadius="8">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <materialDesign:PackIcon Grid.Row="0" Kind="PackageVariantClosed" Width="32" Height="32" HorizontalAlignment="Right" Foreground="White"/>
                    <TextBlock Grid.Row="1" Text="عدد المنتجات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,10,0,5" Foreground="White"/>
                    <TextBlock Grid.Row="2" Text="120" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right" Foreground="White"/>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>

            <!-- آخر الفواتير -->
            <materialDesign:Card Grid.Column="0" Margin="5" Padding="15" UniformCornerRadius="8">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="آخر الفواتير" Style="{StaticResource SectionTitle}"/>

                    <DataGrid Grid.Row="1" Margin="0,10,0,0" Style="{StaticResource DataGridStyle}" FlowDirection="RightToLeft">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الفاتورة" Width="100" Binding="{Binding InvoiceNumber}"/>
                            <DataGridTextColumn Header="التاريخ" Width="120" Binding="{Binding Date, StringFormat=yyyy-MM-dd}"/>
                            <DataGridTextColumn Header="العميل/المورد" Width="150" Binding="{Binding CustomerName}"/>
                            <DataGridTextColumn Header="النوع" Width="100" Binding="{Binding Type}"/>
                            <DataGridTextColumn Header="المبلغ" Width="120" Binding="{Binding Amount, StringFormat=N2}"/>
                            <DataGridTextColumn Header="الحالة" Width="100" Binding="{Binding Status}"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- المنتجات منخفضة المخزون -->
            <materialDesign:Card Grid.Column="1" Margin="5" Padding="15" UniformCornerRadius="8">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="منتجات منخفضة المخزون" Style="{StaticResource SectionTitle}"/>

                    <ListView Grid.Row="1" Margin="0,10,0,0" FlowDirection="RightToLeft">
                        <ListViewItem>
                            <Grid Width="300">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="لوح خشب متوسط" FontWeight="SemiBold"/>
                                    <TextBlock Text="الكمية المتبقية: 5" Foreground="Red" FontSize="12"/>
                                </StackPanel>

                                <Button Grid.Column="1" Style="{StaticResource MaterialDesignIconButton}" ToolTip="طلب شراء">
                                    <materialDesign:PackIcon Kind="CartPlus" Width="20" Height="20"/>
                                </Button>
                            </Grid>
                        </ListViewItem>

                        <ListViewItem>
                            <Grid Width="300">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="مسامير 5 سم" FontWeight="SemiBold"/>
                                    <TextBlock Text="الكمية المتبقية: 20" Foreground="Red" FontSize="12"/>
                                </StackPanel>

                                <Button Grid.Column="1" Style="{StaticResource MaterialDesignIconButton}" ToolTip="طلب شراء">
                                    <materialDesign:PackIcon Kind="CartPlus" Width="20" Height="20"/>
                                </Button>
                            </Grid>
                        </ListViewItem>

                        <ListViewItem>
                            <Grid Width="300">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="دهان أبيض" FontWeight="SemiBold"/>
                                    <TextBlock Text="الكمية المتبقية: 3" Foreground="Red" FontSize="12"/>
                                </StackPanel>

                                <Button Grid.Column="1" Style="{StaticResource MaterialDesignIconButton}" ToolTip="طلب شراء">
                                    <materialDesign:PackIcon Kind="CartPlus" Width="20" Height="20"/>
                                </Button>
                            </Grid>
                        </ListViewItem>
                    </ListView>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- الإجراءات السريعة -->
        <materialDesign:Card Grid.Row="3" Margin="5,20,5,0" Padding="15" UniformCornerRadius="8">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Margin="10" Style="{StaticResource ActionButton}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="CartPlus" Width="20" Height="20" VerticalAlignment="Center"/>
                        <TextBlock Text="فاتورة مبيعات جديدة" Margin="8,0,0,0" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Margin="10" Style="{StaticResource ActionButton}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ShoppingCartPlus" Width="20" Height="20" VerticalAlignment="Center"/>
                        <TextBlock Text="فاتورة مشتريات جديدة" Margin="8,0,0,0" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Margin="10" Style="{StaticResource ActionButton}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="AccountPlusOutline" Width="20" Height="20" VerticalAlignment="Center"/>
                        <TextBlock Text="عميل جديد" Margin="8,0,0,0" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Margin="10" Style="{StaticResource ActionButton}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="FileDocumentOutline" Width="20" Height="20" VerticalAlignment="Center"/>
                        <TextBlock Text="تقرير المبيعات" Margin="8,0,0,0" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</Page>
