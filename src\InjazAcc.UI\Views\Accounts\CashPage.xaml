<Page x:Class="InjazAcc.UI.Views.Accounts.CashPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Accounts"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="900"
      Title="الخزينة / النقدية"
      FlowDirection="RightToLeft"
      FontFamily="Arial"
      TextElement.FontSize="14"
      TextElement.FontWeight="Regular"
      TextElement.Foreground="{DynamicResource MaterialDesignBody}"
      Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة وزر الرجوع -->
        <Grid Grid.Row="0" Margin="20,20,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0" Style="{StaticResource MaterialDesignFlatButton}"
                    ToolTip="الرجوع للحسابات" Click="btnBackToAccounts_Click"
                    HorizontalAlignment="Left" Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ArrowRight" Width="24" Height="24" VerticalAlignment="Center"/>
                    <TextBlock Text="الرجوع للحسابات" VerticalAlignment="Center" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>

            <TextBlock Grid.Column="1" Text="الخزينة / النقدية" Style="{StaticResource PageTitle}"/>
        </Grid>

        <!-- أدوات البحث والإضافة -->
        <Grid Grid.Row="1" Margin="20,0,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- البحث والفلترة -->
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <DatePicker x:Name="dpFromDate" Width="150" materialDesign:HintAssist.Hint="من تاريخ" Margin="0,0,10,0"/>
                <DatePicker x:Name="dpToDate" Width="150" materialDesign:HintAssist.Hint="إلى تاريخ" Margin="0,0,10,0"/>
                <ComboBox x:Name="cmbTransactionType" Width="150" SelectedIndex="0" Margin="0,0,10,0">
                    <ComboBoxItem Content="الكل"/>
                    <ComboBoxItem Content="إيراد"/>
                    <ComboBoxItem Content="مصروف"/>
                    <ComboBoxItem Content="دفع لمورد"/>
                    <ComboBoxItem Content="تحصيل من عميل"/>
                </ComboBox>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="بحث" Margin="0,0,10,0" Click="btnSearch_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="عرض الكل" Click="btnShowAll_Click"/>
            </StackPanel>

            <!-- أزرار الإجراءات -->
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="إضافة إيراد" Margin="0,0,10,0" Click="btnAddIncome_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="إضافة مصروف" Margin="0,0,10,0" Click="btnAddExpense_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="دفع لمورد" Margin="0,0,10,0" Click="btnPaySupplier_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="تحصيل من عميل" Click="btnReceiveFromCustomer_Click"/>
            </StackPanel>
        </Grid>

        <!-- جدول حركات الخزينة -->
        <DataGrid Grid.Row="2" x:Name="dgCashTransactions" AutoGenerateColumns="False" CanUserAddRows="False"
                  Style="{StaticResource DataGridStyle}" Margin="20,0,20,10">
            <DataGrid.Columns>
                <DataGridTextColumn Header="رقم العملية" Binding="{Binding TransactionNumber}" Width="100"/>
                <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=yyyy-MM-dd}" Width="100"/>
                <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="120"/>
                <DataGridTextColumn Header="البيان" Binding="{Binding Description}" Width="*"/>
                <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="120"/>
                <DataGridTextColumn Header="الحساب المقابل" Binding="{Binding RelatedAccount}" Width="150"/>
                <DataGridTemplateColumn Width="100">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="تفاصيل" Click="btnTransactionDetails_Click">
                                    <materialDesign:PackIcon Kind="Information" Width="18" Height="18"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="طباعة" Click="btnPrintTransaction_Click">
                                    <materialDesign:PackIcon Kind="Printer" Width="18" Height="18"/>
                                </Button>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- شريط الإحصائيات -->
        <Grid Grid.Row="3" Margin="20,0,20,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Border Grid.Column="0" Style="{StaticResource StatCard}" Background="#4CAF50" Margin="0,0,5,0">
                <StackPanel>
                    <TextBlock Text="رصيد الخزينة" Style="{StaticResource StatTitle}"/>
                    <TextBlock x:Name="txtCashBalance" Text="0.00 ر.س" Style="{StaticResource StatValue}"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="1" Style="{StaticResource StatCard}" Background="#2196F3" Margin="5,0">
                <StackPanel>
                    <TextBlock Text="إجمالي الإيرادات" Style="{StaticResource StatTitle}"/>
                    <TextBlock x:Name="txtTotalIncome" Text="0.00 ر.س" Style="{StaticResource StatValue}"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="2" Style="{StaticResource StatCard}" Background="#FF9800" Margin="5,0">
                <StackPanel>
                    <TextBlock Text="إجمالي المصروفات" Style="{StaticResource StatTitle}"/>
                    <TextBlock x:Name="txtTotalExpenses" Text="0.00 ر.س" Style="{StaticResource StatValue}"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="3" Style="{StaticResource StatCard}" Background="#9C27B0" Margin="5,0,0,0">
                <StackPanel>
                    <TextBlock Text="عدد العمليات" Style="{StaticResource StatTitle}"/>
                    <TextBlock x:Name="txtTransactionCount" Text="0" Style="{StaticResource StatValue}"/>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Page>
