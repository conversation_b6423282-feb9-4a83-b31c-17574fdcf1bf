using System;
using System.Collections.Generic;

namespace InjazAcc.Core.Models.FinancialStatements
{
    /// <summary>
    /// نموذج نتائج الحسابات الختامية
    /// </summary>
    public class FinancialStatementResult
    {
        /// <summary>
        /// تاريخ بداية الفترة
        /// </summary>
        public DateTime FromDate { get; set; }
        
        /// <summary>
        /// تاريخ نهاية الفترة
        /// </summary>
        public DateTime ToDate { get; set; }
        
        /// <summary>
        /// عناصر ميزان المراجعة
        /// </summary>
        public List<TrialBalanceEntry> TrialBalanceEntries { get; set; } = new List<TrialBalanceEntry>();
        
        /// <summary>
        /// إجمالي الأرصدة المدينة
        /// </summary>
        public decimal TotalDebit { get; set; }
        
        /// <summary>
        /// إجمالي الأرصدة الدائنة
        /// </summary>
        public decimal TotalCredit { get; set; }
        
        /// <summary>
        /// عناصر قائمة الدخل
        /// </summary>
        public List<IncomeStatementEntry> IncomeStatementEntries { get; set; } = new List<IncomeStatementEntry>();
        
        /// <summary>
        /// إجمالي الإيرادات
        /// </summary>
        public decimal TotalRevenues { get; set; }
        
        /// <summary>
        /// إجمالي المصروفات
        /// </summary>
        public decimal TotalExpenses { get; set; }
        
        /// <summary>
        /// صافي الربح أو الخسارة
        /// </summary>
        public decimal NetProfit { get; set; }
        
        /// <summary>
        /// عناصر الأصول في الميزانية
        /// </summary>
        public List<BalanceSheetEntry> AssetEntries { get; set; } = new List<BalanceSheetEntry>();
        
        /// <summary>
        /// عناصر الخصوم وحقوق الملكية في الميزانية
        /// </summary>
        public List<BalanceSheetEntry> LiabilitiesEquityEntries { get; set; } = new List<BalanceSheetEntry>();
        
        /// <summary>
        /// إجمالي الأصول
        /// </summary>
        public decimal TotalAssets { get; set; }
        
        /// <summary>
        /// إجمالي الخصوم وحقوق الملكية
        /// </summary>
        public decimal TotalLiabilitiesEquity { get; set; }
    }
}
