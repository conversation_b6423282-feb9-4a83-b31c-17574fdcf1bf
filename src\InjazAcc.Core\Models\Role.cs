using System.Collections.Generic;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج الدور (الصلاحية) في النظام
    /// </summary>
    public class Role
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        
        // العلاقة مع المستخدمين
        public virtual ICollection<UserRole> UserRoles { get; set; }
        
        // العلاقة مع الصلاحيات
        public virtual ICollection<RolePermission> RolePermissions { get; set; }
    }
}
