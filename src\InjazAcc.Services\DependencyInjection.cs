using Microsoft.Extensions.DependencyInjection;
using InjazAcc.Core.Interfaces;

namespace InjazAcc.Services
{
    /// <summary>
    /// تسجيل الخدمات في حاوية الاعتمادية
    /// </summary>
    public static class DependencyInjection
    {
        /// <summary>
        /// إضافة خدمات التطبيق إلى حاوية الاعتمادية
        /// </summary>
        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            // تسجيل خدمة القيود المحاسبية
            services.AddScoped<IAccountingService, AccountingService>();
            
            // يمكن إضافة المزيد من الخدمات هنا
            
            return services;
        }
    }
}
