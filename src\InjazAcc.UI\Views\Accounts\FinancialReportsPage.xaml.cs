using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace InjazAcc.UI.Views.Accounts
{
    /// <summary>
    /// Interaction logic for FinancialReportsPage.xaml
    /// </summary>
    public partial class FinancialReportsPage : Page
    {
        public FinancialReportsPage()
        {
            InitializeComponent();
        }

        private void TrialBalance_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // الانتقال إلى صفحة الحسابات الختامية مع تحديد تبويب ميزان المراجعة
                NavigationService.Navigate(new FinancialStatementsPage());
                MessageBox.Show("تم الانتقال إلى تقرير ميزان المراجعة", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح تقرير ميزان المراجعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void IncomeStatement_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // الانتقال إلى صفحة الحسابات الختامية مع تحديد تبويب قائمة الدخل
                NavigationService.Navigate(new FinancialStatementsPage());
                MessageBox.Show("تم الانتقال إلى تقرير قائمة الدخل", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح تقرير قائمة الدخل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BalanceSheet_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // الانتقال إلى صفحة الحسابات الختامية مع تحديد تبويب الميزانية العمومية
                NavigationService.Navigate(new FinancialStatementsPage());
                MessageBox.Show("تم الانتقال إلى تقرير الميزانية العمومية", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح تقرير الميزانية العمومية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CashFlow_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                MessageBox.Show("تقرير التدفقات النقدية قيد التطوير", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AccountMovements_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // الانتقال إلى صفحة حساب الأستاذ
                NavigationService.Navigate(new LedgerPage());
                MessageBox.Show("تم الانتقال إلى تقرير حركة الحسابات", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح تقرير حركة الحسابات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ComparePeriods_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                MessageBox.Show("تقرير مقارنة الفترات قيد التطوير", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
