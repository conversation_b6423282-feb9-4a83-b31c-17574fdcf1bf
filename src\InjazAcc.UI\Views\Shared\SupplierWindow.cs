using System;
using System.Windows;
using System.Windows.Input;

namespace InjazAcc.UI.Views.Shared
{
    /// <summary>
    /// نافذة إضافة/تعديل مورد
    /// </summary>
    public class SupplierWindow : Window
    {
        public SupplierWindow()
        {
            try
            {
                // تهيئة النافذة
                Title = "إضافة مورد جديد";
                Width = 600;
                Height = 500;
                WindowStartupLocation = WindowStartupLocation.CenterScreen;

                // إظهار رسالة مؤقتة
                MessageBox.Show("نافذة إضافة مورد جديد - قيد التطوير", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);

                // إغلاق النافذة
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة نافذة المورد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
