using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace InjazAcc.UI.Views.Accounts
{
    /// <summary>
    /// Interaction logic for AccountsPage.xaml
    /// </summary>
    public partial class AccountsPage : Page
    {
        public AccountsPage()
        {
            InitializeComponent();
        }

        private void OpeningBalances_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                NavigationService.Navigate(new OpeningBalancesPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة أرصدة أول المدة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Cash_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                NavigationService.Navigate(new CashPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة الخزينة/النقدية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void FinancialStatements_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                NavigationService.Navigate(new FinancialStatementsPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة الحسابات الختامية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Ledger_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                NavigationService.Navigate(new LedgerPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة حساب الأستاذ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ChartOfAccounts_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                NavigationService.Navigate(new ChartOfAccountsPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة دليل الحسابات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void FinancialReports_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                NavigationService.Navigate(new FinancialReportsPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة التقارير المالية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
