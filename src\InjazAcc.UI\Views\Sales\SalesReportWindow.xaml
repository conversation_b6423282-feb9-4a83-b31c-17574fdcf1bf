<Window x:Class="InjazAcc.UI.Views.Sales.SalesReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Sales"
        mc:Ignorable="d"
        Title="تقرير المبيعات" Height="650" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        TextElement.FontSize="14"
        TextElement.FontWeight="Regular"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- عنوان التقرير -->
        <TextBlock Grid.Row="0" Text="تقرير المبيعات" Style="{StaticResource PageTitle}" HorizontalAlignment="Center"/>
        
        <!-- معايير التقرير -->
        <Grid Grid.Row="1" Margin="0,20,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- نوع التقرير -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="نوع التقرير:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <ComboBox Grid.Row="0" Grid.Column="1" x:Name="cmbReportType" SelectedIndex="0" Margin="0,0,20,0">
                <ComboBoxItem Content="تقرير المبيعات اليومي"/>
                <ComboBoxItem Content="تقرير المبيعات الشهري"/>
                <ComboBoxItem Content="تقرير المبيعات حسب العملاء"/>
                <ComboBoxItem Content="تقرير المبيعات حسب المنتجات"/>
                <ComboBoxItem Content="تقرير مردودات المبيعات"/>
            </ComboBox>
            
            <!-- الفترة -->
            <TextBlock Grid.Row="0" Grid.Column="2" Text="من تاريخ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <DatePicker Grid.Row="0" Grid.Column="3" x:Name="dpFromDate" Margin="0,0,20,0"/>
            
            <TextBlock Grid.Row="1" Grid.Column="2" Text="إلى تاريخ:" VerticalAlignment="Center" Margin="0,10,10,0"/>
            <DatePicker Grid.Row="1" Grid.Column="3" x:Name="dpToDate" Margin="0,10,20,0"/>
            
            <!-- زر عرض التقرير -->
            <Button Grid.Row="0" Grid.Column="4" Grid.RowSpan="2" Content="عرض التقرير" Style="{StaticResource ActionButton}" Click="btnShowReport_Click"/>
        </Grid>
        
        <!-- محتوى التقرير -->
        <TabControl Grid.Row="2" Style="{StaticResource MaterialDesignTabControl}">
            <!-- تقرير المبيعات -->
            <TabItem Header="تقرير المبيعات">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- جدول المبيعات -->
                    <DataGrid Grid.Row="0" x:Name="dgSalesReport" Style="{StaticResource DataGridStyle}" AutoGenerateColumns="False" IsReadOnly="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=yyyy-MM-dd}" Width="120"/>
                            <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                            <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}" Width="200"/>
                            <DataGridTextColumn Header="إجمالي المبيعات" Binding="{Binding TotalSales, StringFormat=N2}" Width="150"/>
                            <DataGridTextColumn Header="إجمالي الخصومات" Binding="{Binding TotalDiscounts, StringFormat=N2}" Width="150"/>
                            <DataGridTextColumn Header="إجمالي الضرائب" Binding="{Binding TotalTaxes, StringFormat=N2}" Width="150"/>
                            <DataGridTextColumn Header="صافي المبيعات" Binding="{Binding NetSales, StringFormat=N2}" Width="150"/>
                        </DataGrid.Columns>
                    </DataGrid>
                    
                    <!-- ملخص التقرير -->
                    <Grid Grid.Row="1" Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="1" Text="إجمالي المبيعات:" FontWeight="Bold" Margin="0,0,10,0"/>
                        <TextBlock Grid.Column="2" x:Name="txtTotalSales" Text="25,000.00 ر.س" FontWeight="Bold" Width="150" TextAlignment="Left"/>
                    </Grid>
                </Grid>
            </TabItem>
            
            <!-- الرسم البياني -->
            <TabItem Header="الرسم البياني">
                <Grid>
                    <TextBlock Text="الرسم البياني للمبيعات" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="18"/>
                </Grid>
            </TabItem>
        </TabControl>
        
        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Style="{StaticResource ActionButton}" Content="طباعة التقرير" Margin="5,0" Click="btnPrintReport_Click"/>
            <Button Style="{StaticResource ActionButton}" Content="تصدير إلى Excel" Margin="5,0" Click="btnExportToExcel_Click"/>
            <Button Style="{StaticResource ActionButton}" Content="إغلاق" Margin="5,0" Click="btnClose_Click"/>
        </StackPanel>
    </Grid>
</Window>
