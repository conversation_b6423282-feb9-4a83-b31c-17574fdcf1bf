using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace InjazAcc.UI.Views.Suppliers
{
    /// <summary>
    /// Interaction logic for SuppliersPage.xaml
    /// </summary>
    public partial class SuppliersPage : Page
    {
        private ObservableCollection<Supplier> _suppliers;

        public SuppliersPage()
        {
            InitializeComponent();
            LoadSampleData();
            UpdateStatistics();
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية للموردين
                _suppliers = new ObservableCollection<Supplier>
                {
                    new Supplier { Code = "S001", Name = "شركة التوريدات العامة", Phone = "0555111222", Email = "<EMAIL>", Address = "الرياض - شارع العليا", Balance = 25000 },
                    new Supplier { Code = "S002", Name = "مؤسسة الإمداد", Phone = "0555333444", Email = "<EMAIL>", Address = "جدة - شارع فلسطين", Balance = 18000 },
                    new Supplier { Code = "S003", Name = "شركة المواد الأولية", Phone = "0555555666", Email = "<EMAIL>", Address = "الدمام - شارع الملك فهد", Balance = 32000 },
                    new Supplier { Code = "S004", Name = "مؤسسة التجهيزات", Phone = "0555777888", Email = "<EMAIL>", Address = "الرياض - شارع التخصصي", Balance = 15000 },
                    new Supplier { Code = "S005", Name = "شركة المعدات المكتبية", Phone = "0555999000", Email = "<EMAIL>", Address = "مكة - شارع الحج", Balance = 22000 }
                };
                
                dgSuppliers.ItemsSource = _suppliers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            try
            {
                // تحديث الإحصائيات
                txtTotalSuppliers.Text = _suppliers.Count.ToString();
                
                double totalPurchases = 0;
                double totalDue = 0;
                
                foreach (var supplier in _suppliers)
                {
                    totalPurchases += supplier.TotalPurchases;
                    totalDue += supplier.Balance;
                }
                
                txtTotalPurchases.Text = totalPurchases.ToString("N2") + " ر.س";
                txtTotalDue.Text = totalDue.ToString("N2") + " ر.س";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث الإحصائيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string searchText = txtSearch.Text.Trim();
                if (string.IsNullOrEmpty(searchText))
                {
                    dgSuppliers.ItemsSource = _suppliers;
                    return;
                }

                string searchBy = (cmbSearchBy.SelectedItem as ComboBoxItem)?.Content.ToString();
                var filteredSuppliers = new ObservableCollection<Supplier>();

                foreach (var supplier in _suppliers)
                {
                    bool isMatch = false;

                    switch (searchBy)
                    {
                        case "الاسم":
                            isMatch = supplier.Name.Contains(searchText);
                            break;
                        case "رقم الهاتف":
                            isMatch = supplier.Phone.Contains(searchText);
                            break;
                        case "البريد الإلكتروني":
                            isMatch = supplier.Email.Contains(searchText);
                            break;
                        case "العنوان":
                            isMatch = supplier.Address.Contains(searchText);
                            break;
                        default:
                            isMatch = supplier.Name.Contains(searchText) || supplier.Phone.Contains(searchText);
                            break;
                    }

                    if (isMatch)
                    {
                        filteredSuppliers.Add(supplier);
                    }
                }

                dgSuppliers.ItemsSource = filteredSuppliers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnAddSupplier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var supplierWindow = new SupplierWindow();
                if (supplierWindow.ShowDialog() == true)
                {
                    _suppliers.Add(supplierWindow.Supplier);
                    UpdateStatistics();
                    MessageBox.Show("تم إضافة المورد بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEditSupplier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var supplier = button.DataContext as Supplier;
                
                if (supplier != null)
                {
                    var supplierWindow = new SupplierWindow(supplier);
                    if (supplierWindow.ShowDialog() == true)
                    {
                        int index = _suppliers.IndexOf(supplier);
                        if (index >= 0)
                        {
                            _suppliers[index] = supplierWindow.Supplier;
                            dgSuppliers.Items.Refresh();
                            UpdateStatistics();
                            MessageBox.Show("تم تعديل المورد بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnDeleteSupplier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var supplier = button.DataContext as Supplier;
                
                if (supplier != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف المورد: {supplier.Name}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    
                    if (result == MessageBoxResult.Yes)
                    {
                        _suppliers.Remove(supplier);
                        UpdateStatistics();
                        MessageBox.Show("تم حذف المورد بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSupplierDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var supplier = button.DataContext as Supplier;
                
                if (supplier != null)
                {
                    var supplierDetailsWindow = new SupplierDetailsWindow(supplier);
                    supplierDetailsWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void dgSuppliers_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (dgSuppliers.SelectedItem is Supplier supplier)
                {
                    var supplierDetailsWindow = new SupplierDetailsWindow(supplier);
                    supplierDetailsWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class Supplier
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public double Balance { get; set; }
        public double TotalPurchases { get; set; } = 0; // قيمة افتراضية للمشتريات
    }
}
